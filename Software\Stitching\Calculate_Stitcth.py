import sys
import cv2
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QPushButton,
    QFileDialog, QLabel, QSpinBox, QHBoxLayout, QMessageBox, QScrollArea
)
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt


class StitcherApp(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Zig-Zag Grid Stitcher (Phase Correlation Only)")
        self.image_paths = []
        self.preview_label = QLabel()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        self.label = QLabel("Belum ada gambar dipilih")
        layout.addWidget(self.label)

        btn_select = QPushButton("Pilih Gambar")
        btn_select.clicked.connect(self.select_images)
        layout.addWidget(btn_select)

        # Grid parameters
        grid_layout = QHBoxLayout()
        grid_layout.addWidget(QLabel("Rows:"))
        self.row_spin = QSpinBox()
        self.row_spin.setMinimum(1)
        grid_layout.addWidget(self.row_spin)
        grid_layout.addWidget(QLabel("Cols:"))
        self.col_spin = QSpinBox()
        self.col_spin.setMinimum(1)
        grid_layout.addWidget(self.col_spin)
        layout.addLayout(grid_layout)

        # Stitch button
        btn_stitch = QPushButton("Stitch (Phase Correlation)")
        btn_stitch.clicked.connect(self.run_stitch)
        layout.addWidget(btn_stitch)

        # Scroll area for preview
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(self.preview_label)
        layout.addWidget(scroll)

        self.setLayout(layout)

    def select_images(self):
        files, _ = QFileDialog.getOpenFileNames(
            self, "Pilih gambar", "", "Images (*.jpg *.png *.jpeg)"
        )
        if files:
            self.image_paths = sorted(files)
            self.label.setText(f"{len(self.image_paths)} gambar dipilih")
            self.update_preview()

    def update_preview(self):
        rows = self.row_spin.value()
        cols = self.col_spin.value()
        total = rows * cols
        if len(self.image_paths) < total or total == 0:
            return

        # load images kecil untuk preview
        thumbs = []
        for p in self.image_paths[:total]:
            img = cv2.imread(p)
            if img is None:
                continue
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            thumbs.append(img)

        # buat canvas preview sesuai rows/cols
        h, w = 100, 100  # ukuran tile sementara
        canvas = np.ones((rows * h, cols * w, 3), np.uint8) * 255

        idx = 0
        for r in range(rows):
            # zigzag
            if r % 2 == 0:
                cols_order = range(cols)
            else:
                cols_order = reversed(range(cols))
            for c in cols_order:
                if idx >= len(thumbs):
                    break
                thumb = cv2.resize(thumbs[idx], (w, h))
                y, x = r * h, c * w
                canvas[y:y+h, x:x+w] = thumb
                idx += 1

        # scale canvas supaya max 500x500
        ch, cw = canvas.shape[:2]
        scale = min(500 / ch, 500 / cw, 1.0)
        new_size = (int(cw * scale), int(ch * scale))
        canvas = cv2.resize(canvas, new_size)

        qimg = QImage(canvas.data, canvas.shape[1], canvas.shape[0],
                      canvas.strides[0], QImage.Format_RGB888)
        self.preview_label.setPixmap(QPixmap.fromImage(qimg))

    def run_stitch(self):
        rows = self.row_spin.value()
        cols = self.col_spin.value()
        total = rows * cols
        if len(self.image_paths) < total:
            QMessageBox.warning(self, "Error", f"Diperlukan {total} gambar, sekarang {len(self.image_paths)}.")
            return

        # load images in zigzag order
        imgs = []
        idx = 0
        for r in range(rows):
            row_imgs = []
            if r % 2 == 0:
                cols_order = range(cols)
            else:
                cols_order = reversed(range(cols))
            for _ in cols_order:
                img = cv2.imread(self.image_paths[idx])
                if img is None:
                    QMessageBox.warning(self, "Error", f"Gagal baca: {self.image_paths[idx]}")
                    return
                row_imgs.append(img)
                idx += 1
            imgs.append(row_imgs)

        result = self.stitch_grid_phase(imgs)

        save_path, _ = QFileDialog.getSaveFileName(
            self, "Simpan hasil stitching", "output.jpg", "Images (*.jpg *.png)"
        )
        if save_path:
            cv2.imwrite(save_path, result)
            QMessageBox.information(self, "Sukses", f"Tersimpan di:\n{save_path}")

    def compute_phase(self, img1, img2):
        g1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY).astype(np.float32)
        g2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY).astype(np.float32)
        shift, response = cv2.phaseCorrelate(g1, g2)
        if response < 0.05:
            return 0, 0
        return shift[0], shift[1]

    def stitch_grid_phase(self, images):
        rows = len(images)
        cols = len(images[0])
        h, w = images[0][0].shape[:2]

        default_overlap = 0.2
        coarse_step_x = int(w * (1 - default_overlap))
        coarse_step_y = int(h * (1 - default_overlap))

        pos = [[None]*cols for _ in range(rows)]
        pos[0][0] = (0, 0)

        for r in range(rows):
            for c in range(cols):
                if r == 0 and c == 0:
                    continue
                if c > 0:
                    x0, y0 = pos[r][c-1]
                    cx, cy = x0 + coarse_step_x, y0
                    img_ref = images[r][c-1]
                else:
                    x0, y0 = pos[r-1][c]
                    cx, cy = x0, y0 - coarse_step_y
                    img_ref = images[r-1][c]
                img_cur = images[r][c]

                dx, dy = self.compute_phase(img_ref, img_cur)
                pos[r][c] = (int(cx + dx), int(cy + dy))

        xs = [p[0] for row in pos for p in row]
        ys = [p[1] for row in pos for p in row]
        minx, miny = min(xs), min(ys)
        for r in range(rows):
            for c in range(cols):
                x, y = pos[r][c]
                pos[r][c] = (x - minx, y - miny)

        maxx = max(p[0] for row in pos for p in row) + w
        maxy = max(p[1] for row in pos for p in row) + h
        canvas = np.zeros((maxy, maxx, 3), np.uint8)

        for r in range(rows):
            for c in range(cols):
                x, y = pos[r][c]
                img = images[r][c]
                img_h, img_w = img.shape[:2]
                canvas[y:y+img_h, x:x+img_w] = img

        return canvas


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = StitcherApp()
    win.resize(600, 500)
    win.show()
    sys.exit(app.exec_())
