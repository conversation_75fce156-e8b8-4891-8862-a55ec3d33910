"""
Enhanced Mapping AF with Z Interpolation
Combines the original MappingAF with Z interpolation for improved efficiency
"""

import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QThread, pyqtSlot
import time
from .Mapping_AF import MappingAF<PERSON>orker
from .Z_Interpolation import ZInterpolation

class EnhancedMappingAFWorker(QObject):
    """
    Enhanced version of MappingAF that includes Z interpolation
    """
    finished = pyqtSignal()
    progress = pyqtSignal(int, str)
    log_message = pyqtSignal(str)
    interpolation_ready = pyqtSignal(object)  # Emits ZInterpolation object when ready

    def __init__(self, camera, grbl, grbl_start, grbl_end, offset, enable_interpolation=True):
        super().__init__()
        self.camera = camera
        self.grbl = grbl
        self.grbl_start = grbl_start
        self.grbl_end = grbl_end
        self.offset = offset
        self.enable_interpolation = enable_interpolation
        
        # Create the original mapping worker
        self.mapping_worker = MappingAFWorker(camera, grbl, grbl_start, grbl_end, self.offset)
        
        # Z interpolation object
        self.z_interpolation = None
        
        # Connect signals from mapping worker
        self.mapping_worker.finished.connect(self.on_mapping_finished)
        self.mapping_worker.progress.connect(self.progress.emit)
        self.mapping_worker.log_message.connect(self.log_message.emit)
        
    def run(self):
        """
        Run enhanced mapping with interpolation
        """
        try:
            self.log_message.emit("\n" + "="*60)
            self.log_message.emit("ENHANCED MAPPING AF WITH Z INTERPOLATION")
            self.log_message.emit("="*60)
            self.log_message.emit(f"Interpolation enabled: {self.enable_interpolation}")
            
            # Start the original mapping process
            self.log_message.emit("Phase 1: Running 3x3 grid mapping...")
            self.mapping_worker.run()
            
        except Exception as e:
            self.log_message.emit(f"Error in Enhanced Mapping AF: {e}")
            self.finished.emit()
    
    @pyqtSlot()
    def on_mapping_finished(self):
        """
        Called when the original mapping is finished
        """
        try:
            self.log_message.emit("\n" + "-"*60)
            self.log_message.emit("Phase 1 Complete: 3x3 grid mapping finished")
            self.log_message.emit("-"*60)
            
            if not self.enable_interpolation:
                self.log_message.emit("Interpolation disabled - Enhanced mapping complete")
                self.finished.emit()
                return
            
            # Check if we have valid mapping results
            if not hasattr(self.mapping_worker, 'results') or not self.mapping_worker.results:
                self.log_message.emit("❌ No mapping results available for interpolation")
                self.finished.emit()
                return
            
            # Start interpolation phase
            self.log_message.emit("Phase 2: Creating Z interpolation...")
            self.create_z_interpolation()
            
        except Exception as e:
            self.log_message.emit(f"Error in on_mapping_finished: {e}")
            self.finished.emit()
    
    def create_z_interpolation(self):
        """
        Create Z interpolation from mapping results
        """
        try:
            # Create interpolation object
            self.z_interpolation = ZInterpolation(
                self.grbl_start, 
                self.grbl_end, 
                self.mapping_worker.results
            )
            
            # Perform interpolation
            self.log_message.emit("Creating 6x6 interpolated grid...")
            
            if self.z_interpolation.create_interpolation_grid():
                self.log_message.emit("✓ Z interpolation successful!")
                
                # Log interpolation results
                interp_log = self.z_interpolation.log_interpolation_results()
                self.log_message.emit(interp_log)
                
                # Save interpolation data
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                filename = f"z_interpolation_{timestamp}.txt"
                self.z_interpolation.save_interpolation_data(filename)
                self.log_message.emit(f"✓ Interpolation data saved to: {filename}")
                
                # Emit signal with interpolation object
                self.interpolation_ready.emit(self.z_interpolation)
                
                self.log_message.emit("\n" + "="*60)
                self.log_message.emit("ENHANCED MAPPING AF COMPLETE")
                self.log_message.emit("="*60)
                self.log_message.emit("✓ 3x3 grid mapping complete")
                self.log_message.emit("✓ 6x6 Z interpolation complete")
                self.log_message.emit("✓ Ready for high-precision auto-focus")
                
            else:
                self.log_message.emit("❌ Z interpolation failed")
                
        except Exception as e:
            self.log_message.emit(f"Error creating Z interpolation: {e}")
        
        finally:
            self.finished.emit()
    
    def get_interpolated_z(self, x, y):
        """
        Get interpolated Z value at specific position
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            float: Interpolated Z value or None if not available
        """
        if self.z_interpolation is None:
            self.log_message.emit("⚠️ Z interpolation not available")
            return None
            
        return self.z_interpolation.get_z_at_position(x, y)
    
    def get_all_interpolated_points(self):
        """
        Get all 6x6 interpolated grid points
        
        Returns:
            dict: Dictionary with (x,y) keys and z values, or None if not available
        """
        if self.z_interpolation is None:
            return None
            
        return self.z_interpolation.get_interpolated_grid_points()
    
    def stop(self):
        """
        Stop the enhanced mapping process
        """
        if self.mapping_worker:
            self.mapping_worker.stop()
        self.log_message.emit("Enhanced Mapping AF stopped by user")

class SmartAutoFocusHelper:
    """
    Helper class for using interpolated Z values in auto-focus operations
    """
    
    def __init__(self, z_interpolation, grbl):
        self.z_interpolation = z_interpolation
        self.grbl = grbl
        
    def move_to_interpolated_z(self, x, y, offset=0.0):
        """
        Move Z axis to interpolated position for given X,Y coordinates
        
        Args:
            x: X coordinate
            y: Y coordinate  
            offset: Additional Z offset (positive = higher, negative = lower)
            
        Returns:
            bool: True if movement successful, False otherwise
        """
        try:
            # Get interpolated Z value
            target_z = self.z_interpolation.get_z_at_position(x, y)
            
            if target_z is None:
                print(f"❌ Cannot get interpolated Z for position ({x:.3f}, {y:.3f})")
                return False
            
            # Apply offset
            final_z = target_z + offset
            
            # Move to position
            print(f"Moving to interpolated Z: {final_z:.4f} (base: {target_z:.4f}, offset: {offset:.4f})")
            self.grbl.move_z(final_z)
            
            return True
            
        except Exception as e:
            print(f"❌ Error moving to interpolated Z: {e}")
            return False
    
    def get_optimal_z_range(self, x, y, range_mm=0.5):
        """
        Get optimal Z range for fine auto-focus based on interpolated value
        
        Args:
            x: X coordinate
            y: Y coordinate
            range_mm: Range around interpolated Z for fine focus (±range_mm)
            
        Returns:
            tuple: (z_min, z_max) or (None, None) if error
        """
        try:
            base_z = self.z_interpolation.get_z_at_position(x, y)
            
            if base_z is None:
                return None, None
            
            z_min = base_z - range_mm
            z_max = base_z + range_mm
            
            return z_min, z_max
            
        except Exception as e:
            print(f"❌ Error calculating optimal Z range: {e}")
            return None, None
    
    def suggest_focus_strategy(self, x, y):
        """
        Suggest auto-focus strategy based on position and interpolated data
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            dict: Strategy information
        """
        try:
            # Get interpolated Z
            interp_z = self.z_interpolation.get_z_at_position(x, y)
            
            if interp_z is None:
                return {"strategy": "full_scan", "reason": "No interpolation data"}
            
            # Check if position is close to original mapping points
            original_points = [(0, 0), (1.5, 0), (3, 0), (0, 1.5), (1.5, 1.5), 
                             (3, 1.5), (0, 3), (1.5, 3), (3, 3)]
            
            min_distance = float('inf')
            for orig_x, orig_y in original_points:
                distance = ((x - orig_x)**2 + (y - orig_y)**2)**0.5
                min_distance = min(min_distance, distance)
            
            if min_distance < 0.1:  # Very close to original point
                return {
                    "strategy": "minimal_scan", 
                    "reason": "Close to mapped point",
                    "suggested_range": 0.2,
                    "base_z": interp_z
                }
            elif min_distance < 0.5:  # Moderately close
                return {
                    "strategy": "fine_scan", 
                    "reason": "Near mapped point",
                    "suggested_range": 0.5,
                    "base_z": interp_z
                }
            else:  # Far from original points
                return {
                    "strategy": "medium_scan", 
                    "reason": "Between mapped points",
                    "suggested_range": 1.0,
                    "base_z": interp_z
                }
                
        except Exception as e:
            return {"strategy": "full_scan", "reason": f"Error: {e}"}
