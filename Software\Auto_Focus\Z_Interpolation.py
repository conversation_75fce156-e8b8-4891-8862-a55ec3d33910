import numpy as np
import time
from scipy.interpolate import griddata, RectBivariateSpline

class ZInterpolation:
    """
    Class for interpolating Z values from a 3x3 grid to a higher resolution grid
    using bilinear interpolation for efficient auto-focus mapping.
    """

    def __init__(self, grbl_start, grbl_end, mapping_results, offset=0.5):
        """
        Initialize Z interpolation with mapping results from a 3x3 grid.

        Args:
            grbl_start: (x_start, y_start) tuple of the original ROI.
            grbl_end: (x_end, y_end) tuple of the original ROI.
            mapping_results: Dictionary with (x,y) keys and z values from the 9-point MappingAF.
            offset: Grid offset for interpolation (default 0.5).
        """
        
        self.grbl_start = grbl_start
        self.grbl_end = grbl_end
        self.mapping_results = mapping_results
        self.offset = offset

        # Extract Z values and focus scores from the new data structure
        self.z_values = {}
        self.focus_scores = {}
        for point, data in mapping_results.items():
            if isinstance(data, dict):
                self.z_values[point] = data.get('z', 0)
                self.focus_scores[point] = data.get('score', 0)
            else:
                # Backward compatibility: if old format (just Z value)
                self.z_values[point] = data
                self.focus_scores[point] = 0

        # The original points are the keys from the mapping results
        x_coords = sorted(list(set(k[0] for k in mapping_results.keys())))
        y_coords = sorted(list(set(k[1] for k in mapping_results.keys())))
        self.x_original = np.array(x_coords)

        # Handle Y coordinates based on GRBL start/end relationship
        if grbl_end[1] < grbl_start[1]:
            # Y terbalik (start > end), sort descending untuk konsistensi
            y_coords = sorted(y_coords, reverse=True)
        self.y_original = np.array(y_coords)

        # Calculate interpolated grid size based on offset relative to the original ROI dimensions
        x_range = grbl_end[0] - grbl_start[0]
        y_range = abs(grbl_end[1] - grbl_start[1])

        if offset > 0:
            self.x_points_count = int(round(x_range / offset)) + 1
            self.y_points_count = int(round(y_range / offset)) + 1
        else:
            self.x_points_count = 3
            self.y_points_count = 3

        # Create the high-density grid points to be interpolated - SPAN FULL ROI
        # NOT just the Mapping AF boundaries
        self.x_interpolated = np.linspace(self.grbl_start[0], self.grbl_end[0], self.x_points_count)

        # Y interpolation - span full ROI in proper order
        if self.grbl_end[1] < self.grbl_start[1]:
            # Y terbalik (start > end)
            self.y_interpolated = np.linspace(self.grbl_start[1], self.grbl_end[1], self.y_points_count)
        else:
            # Y normal (start < end)
            self.y_interpolated = np.linspace(self.grbl_start[1], self.grbl_end[1], self.y_points_count)

        # Store interpolated results for all methods
        self.interpolated_results = {
            'bilinear': {},
            'bicubic': {},
            'spline': {}
        }
        self.interpolated_z_grid = {
            'bilinear': None,
            'bicubic': None,
            'spline': None
        }

        print(f"Grid configuration:")
        print(f"  Original: 3x3 grid (from {len(mapping_results)} mapped points)")
        print(f"  Interpolated: {self.x_points_count}x{self.y_points_count} grid covering FULL ROI")
        print(f"  Offset factor: {offset}")
        print(f"  Total interpolated points: {self.x_points_count * self.y_points_count}")
        print(f"  ROI coverage: X{self.grbl_start[0]:.3f} to {self.grbl_end[0]:.3f}, Y{self.grbl_start[1]:.3f} to {self.grbl_end[1]:.3f}")

    def validate_mapping_data(self):
        """
        Validate that we have all 9 points from the mapping

        Returns:
            bool: True if all points are valid, False otherwise
        """
        expected_points = []
        for i in range(3):
            for j in range(3):
                x = self.x_original[j]
                y = self.y_original[i]
                expected_points.append((x, y))

        missing_points = []
        invalid_z_points = []
        low_focus_score_points = []

        for point in expected_points:
            if point not in self.z_values:
                missing_points.append(point)
            elif self.z_values[point] <= 0:
                invalid_z_points.append(point)
            elif self.focus_scores.get(point, 0) < 1.4:
                low_focus_score_points.append(point)

        if missing_points:
            print(f"X Missing mapping points: {missing_points}")
            return False

        if invalid_z_points:
            print(f"! Invalid Z values (≤0) at points: {invalid_z_points}")
            # Replace invalid Z values with nearest valid neighbor
            self._fix_invalid_z_values(invalid_z_points)

        if low_focus_score_points:
            print(f"! Low focus scores (<1.4) at points: {low_focus_score_points}")
            # Replace Z values with low focus scores with nearest valid neighbor
            self._fix_low_focus_score_points(low_focus_score_points)

        return True

    def _fix_invalid_z_values(self, invalid_points):
        """
        Replace invalid Z values (≤0) with nearest valid neighbor values

        Args:
            invalid_points: List of (x,y) tuples with invalid Z values
        """
        print(f"[Z_INTERPOLATION] Fixing {len(invalid_points)} invalid Z values...")

        for invalid_point in invalid_points:
            # Find nearest valid point
            min_distance = float('inf')
            nearest_z = None

            for point, z_value in self.z_values.items():
                if z_value > 0 and point != invalid_point:
                    distance = ((point[0] - invalid_point[0])**2 + (point[1] - invalid_point[1])**2)**0.5
                    if distance < min_distance:
                        min_distance = distance
                        nearest_z = z_value

            if nearest_z is not None:
                old_z = self.z_values[invalid_point]
                self.z_values[invalid_point] = nearest_z
                print(f"  Fixed point {invalid_point}: Z={old_z:.4f} -> Z={nearest_z:.4f} (distance: {min_distance:.3f})")
            else:
                print(f"  WARNING: No valid neighbor found for point {invalid_point}")

    def _fix_low_focus_score_points(self, low_score_points):
        """
        Replace Z values with low focus scores (<1.4) with nearest valid neighbor values

        Args:
            low_score_points: List of (x,y) tuples with low focus scores
        """
        print(f"[Z_INTERPOLATION] Fixing {len(low_score_points)} points with low focus scores...")

        for invalid_point in low_score_points:
            # Find nearest valid point (valid Z and good focus score)
            min_distance = float('inf')
            nearest_z = None

            for point, z_value in self.z_values.items():
                focus_score = self.focus_scores.get(point, 0)
                if z_value > 0 and focus_score >= 1.4 and point != invalid_point:
                    distance = ((point[0] - invalid_point[0])**2 + (point[1] - invalid_point[1])**2)**0.5
                    if distance < min_distance:
                        min_distance = distance
                        nearest_z = z_value

            if nearest_z is not None:
                old_z = self.z_values[invalid_point]
                old_score = self.focus_scores.get(invalid_point, 0)
                self.z_values[invalid_point] = nearest_z
                print(f"  Fixed point {invalid_point}: Z={old_z:.4f}, Score={old_score:.2f} -> Z={nearest_z:.4f} (distance: {min_distance:.3f})")
            else:
                print(f"  WARNING: No valid neighbor found for point {invalid_point}")

    def _bilinear_interpolate(self, x, y):
        """
        Perform bilinear interpolation for a single point using the 3x3 grid.
        """
        # Find the grid cell containing the point
        x_idx = -1
        y_idx = -1

        if len(self.x_original) < 2 or len(self.y_original) < 2:
             return 0 # Not enough points to interpolate

        for i in range(len(self.x_original) - 1):
            if x >= self.x_original[i] and x <= self.x_original[i+1]:
                x_idx = i
                break
        if x_idx == -1: x_idx = 0 # Handle edge case

        for i in range(len(self.y_original) - 1):
            if y >= self.y_original[i] and y <= self.y_original[i+1]:
                y_idx = i
                break
        if y_idx == -1: y_idx = 0 # Handle edge case

        # Get the four corner points
        x1, x2 = self.x_original[x_idx], self.x_original[x_idx+1]
        y1, y2 = self.y_original[y_idx], self.y_original[y_idx+1]

        # Get Z values at corners
        z11 = self.z_values.get((x1, y1), 0)
        z12 = self.z_values.get((x1, y2), 0)
        z21 = self.z_values.get((x2, y1), 0)
        z22 = self.z_values.get((x2, y2), 0)

        # Fallback to nearest neighbor if a corner has invalid Z
        if any(z <= 0 for z in [z11, z12, z21, z22]):
            distances = []
            for (orig_x, orig_y), z_val in self.z_values.items():
                if z_val > 0:
                    dist = ((x - orig_x)**2 + (y - orig_y)**2)**0.5
                    distances.append((dist, z_val))
            if distances:
                return min(distances, key=lambda item: item[0])[1]
            return 0

        # Perform bilinear interpolation
        if x2 == x1 or y2 == y1: return z11 # Avoid division by zero
        
        t_x = (x - x1) / (x2 - x1)
        t_y = (y - y1) / (y2 - y1)

        z_y1 = z11 * (1 - t_x) + z21 * t_x
        z_y2 = z12 * (1 - t_x) + z22 * t_x
        
        return z_y1 * (1 - t_y) + z_y2 * t_y

    def _create_bilinear_grid(self):
        """Calculates the bilinear interpolation grid."""
        print("Creating bilinear interpolated grid...")
        results = {}
        for y in self.y_interpolated:
            for x in self.x_interpolated:
                results[(x, y)] = self._bilinear_interpolate(x, y)
        
        self.interpolated_results['bilinear'] = results
        grid = np.zeros((self.y_points_count, self.x_points_count))
        for i, y in enumerate(self.y_interpolated):
            for j, x in enumerate(self.x_interpolated):
                grid[i, j] = results.get((x, y), 0)
        self.interpolated_z_grid['bilinear'] = grid
        print("V Bilinear interpolation complete.")

    def _create_scipy_grid(self, method):
        """Calculates interpolation using scipy.griddata for methods like 'cubic' or 'linear'."""
        print(f"Creating {method} interpolated grid...")

        # Prepare source points and values
        points = np.array(list(self.z_values.keys()))
        values = np.array(list(self.z_values.values()))

        # Prepare target grid
        grid_x, grid_y = np.meshgrid(self.x_interpolated, self.y_interpolated)

        # Perform interpolation
        grid_z = griddata(points, values, (grid_x, grid_y), method=method)

        # Store results
        results = {}
        for i, y in enumerate(self.y_interpolated):
            for j, x in enumerate(self.x_interpolated):
                results[(x, y)] = grid_z[i, j]

        self.interpolated_results[method] = results
        self.interpolated_z_grid[method] = grid_z
        print(f"V {method.capitalize()} interpolation complete.")

    def _create_scipy_grid_safe(self, method):
        """Safe version of scipy grid interpolation with NaN handling."""
        print(f"Creating {method} interpolated grid (safe mode)...")

        try:
            # Prepare source points and values
            points = np.array(list(self.z_values.keys()))
            values = np.array(list(self.z_values.values()))

            # Prepare target grid
            grid_x, grid_y = np.meshgrid(self.x_interpolated, self.y_interpolated)

            # Perform interpolation
            grid_z = griddata(points, values, (grid_x, grid_y), method=method)

            # Check for NaN values
            nan_count = np.sum(np.isnan(grid_z))
            if nan_count > 0:
                print(f"! {method.capitalize()} interpolation produced {nan_count} NaN values")
                # Fill NaN with bilinear interpolation as fallback
                bilinear_grid = self._get_bilinear_fallback_grid()
                grid_z = np.where(np.isnan(grid_z), bilinear_grid, grid_z)
                print(f"V Filled NaN values with bilinear interpolation")

            # Store results
            results = {}
            for i, y in enumerate(self.y_interpolated):
                for j, x in enumerate(self.x_interpolated):
                    results[(x, y)] = grid_z[i, j]

            self.interpolated_results[method] = results
            self.interpolated_z_grid[method] = grid_z
            print(f"V {method.capitalize()} interpolation complete (safe mode).")

        except Exception as e:
            print(f"X {method.capitalize()} interpolation failed: {e}")
            raise

    def _create_spline_grid_safe(self):
        """Safe version of spline interpolation with error handling."""
        print("Creating spline interpolated grid (safe mode)...")
        print(f"DEBUG: x_original length: {len(self.x_original)}, y_original length: {len(self.y_original)}")
        print(f"DEBUG: x_original: {self.x_original}")
        print(f"DEBUG: y_original: {self.y_original}")

        try:
            # Ensure coordinates are strictly increasing for spline interpolation
            x_spline = np.sort(self.x_original)
            y_spline = np.sort(self.y_original)

            # Create Z grid with proper ordering
            z_original = np.zeros((len(y_spline), len(x_spline)))
            for i, y in enumerate(y_spline):
                for j, x in enumerate(x_spline):
                    z_original[i, j] = self.z_values.get((x, y), 0)

            # Create spline interpolator with sorted coordinates
            f_spline = RectBivariateSpline(y_spline, x_spline, z_original, kx=2, ky=2)

            # Interpolate on the target grid (which may have different ordering)
            x_interp_sorted = np.sort(self.x_interpolated)
            y_interp_sorted = np.sort(self.y_interpolated)
            spline_grid_sorted = f_spline(y_interp_sorted, x_interp_sorted)

            # Check for NaN or invalid values
            if np.any(np.isnan(spline_grid_sorted)) or np.any(np.isinf(spline_grid_sorted)):
                print("! Spline interpolation produced invalid values, using bilinear fallback")
                spline_grid_sorted = self._get_bilinear_fallback_grid()

            # Reorder results to match original interpolation grid ordering
            spline_grid = np.zeros((len(self.y_interpolated), len(self.x_interpolated)))
            for i, y in enumerate(self.y_interpolated):
                for j, x in enumerate(self.x_interpolated):
                    # Find indices in sorted arrays
                    y_idx = np.where(y_interp_sorted == y)[0][0]
                    x_idx = np.where(x_interp_sorted == x)[0][0]
                    spline_grid[i, j] = spline_grid_sorted[y_idx, x_idx]

            spline_results = {}
            for i, y in enumerate(self.y_interpolated):
                for j, x in enumerate(self.x_interpolated):
                    spline_results[(x, y)] = spline_grid[i, j]
            self.interpolated_results['spline'] = spline_results
            self.interpolated_z_grid['spline'] = spline_grid
            print("V Spline interpolation complete (safe mode).")

        except Exception as e:
            print(f"X Spline interpolation failed: {e}")
            raise

    def _get_bilinear_fallback_grid(self):
        """Get bilinear interpolation grid as fallback."""
        if self.interpolated_z_grid.get('bilinear') is not None:
            return self.interpolated_z_grid['bilinear']

        # Create bilinear grid if not exists
        results = {}
        for y in self.y_interpolated:
            for x in self.x_interpolated:
                results[(x, y)] = self._bilinear_interpolate(x, y)

        grid = np.zeros((self.y_points_count, self.x_points_count))
        for i, y in enumerate(self.y_interpolated):
            for j, x in enumerate(self.x_interpolated):
                grid[i, j] = results.get((x, y), 0)

        return grid

    def create_interpolation_grid(self):
        """
        Create interpolated Z values using reliable methods.
        Prioritizes bilinear as primary method, uses others as secondary.
        """
        try:
            if not self.validate_mapping_data():
                print("X Cannot interpolate: Invalid mapping data")
                return False

            valid_points = sum(1 for z in self.z_values.values() if z > 0)
            if valid_points < 4:
                print(f"X Insufficient valid points for interpolation: {valid_points}")
                return False

            print(f"V Starting interpolation with {valid_points} valid points")

            # --- Primary method: Bilinear (always reliable) ---
            print("Creating bilinear interpolated grid (primary method)...")
            self._create_bilinear_grid()

            # --- Secondary methods: Only if we have enough data ---
            if valid_points >= 6:
                # Try cubic interpolation with error handling
                try:
                    print("Creating cubic interpolated grid...")
                    self._create_scipy_grid_safe('cubic')
                except Exception as e:
                    print(f"! Cubic interpolation failed: {e}, skipping")

                # Try spline interpolation
                try:
                    self._create_spline_grid_safe()
                except Exception as e:
                    print(f"! Spline interpolation failed: {e}, skipping")
            else:
                print(f"Skipping cubic/spline interpolation: only {valid_points} points (need 6+)")

            return True

        except Exception as e:
            print(f"X Error during interpolation: {e}")
            return False
    
    def get_z_at_position(self, x, y, method='bilinear'):
        """
        Get interpolated Z value at a specific X,Y position using a specified method.
        Now handles extrapolation beyond Mapping AF boundaries.
        """
        if method not in self.interpolated_z_grid or self.interpolated_z_grid[method] is None:
            print(f"! {method.capitalize()} interpolation grid not created yet.")
            return None

        # Check if position is within interpolation bounds
        within_bounds = (self.x_interpolated[0] <= x <= self.x_interpolated[-1] and
                        self.y_interpolated[0] <= y <= self.y_interpolated[-1])

        if within_bounds:
            # Use interpolated value
            try:
                # Find the indices corresponding to the coordinates
                x_idx = np.argmin(np.abs(self.x_interpolated - x))
                y_idx = np.argmin(np.abs(self.y_interpolated - y))

                z_value = self.interpolated_z_grid[method][y_idx, x_idx]
                return float(z_value) if z_value > 0 else None

            except Exception as e:
                print(f"X Error getting Z at ({x:.3f}, {y:.3f}) with {method} method: {e}")
                return None
        else:
            # Position is outside interpolation bounds - use extrapolation
            print(f"Position ({x:.3f}, {y:.3f}) is outside interpolation bounds - using extrapolation")
            return self._extrapolate_z_value(x, y, method)

    def _extrapolate_z_value(self, x, y, method='bilinear'):
        """
        Extrapolate Z value for positions outside the interpolation bounds.
        Uses the nearest boundary value as approximation.
        """
        try:
            # Clamp coordinates to the nearest boundary
            x_clamped = np.clip(x, self.x_interpolated[0], self.x_interpolated[-1])
            y_clamped = np.clip(y, self.y_interpolated[0], self.y_interpolated[-1])

            # Get interpolated value at clamped position
            x_idx = np.argmin(np.abs(self.x_interpolated - x_clamped))
            y_idx = np.argmin(np.abs(self.y_interpolated - y_clamped))

            z_value = self.interpolated_z_grid[method][y_idx, x_idx]

            if z_value > 0:
                print(f"Extrapolated Z at ({x:.3f}, {y:.3f}) using boundary value {z_value:.4f}")
                return float(z_value)
            else:
                # Fallback to center value if boundary value is invalid
                center_x_idx = len(self.x_interpolated) // 2
                center_y_idx = len(self.y_interpolated) // 2
                center_z = self.interpolated_z_grid[method][center_y_idx, center_x_idx]
                print(f"Extrapolated Z at ({x:.3f}, {y:.3f}) using center fallback {center_z:.4f}")
                return float(center_z) if center_z > 0 else None

        except Exception as e:
            print(f"X Error extrapolating Z at ({x:.3f}, {y:.3f}): {e}")
            return None
    
    def get_interpolated_grid_points(self):
        """
        Get all interpolated grid points with NaN filtering and method prioritization

        Priority order: spline (most reliable) -> bilinear -> cubic
        Only includes valid (non-NaN, positive) Z values

        Returns:
            dict: Dictionary with (x,y) keys and z values
        """
        all_points = {}

        # Priority order: most reliable to least reliable
        method_priority = ['spline', 'bilinear', 'cubic']

        for method in method_priority:
            if method in self.interpolated_results:
                for (x, y), z in self.interpolated_results[method].items():
                    # Only add if not already present and value is valid
                    if (x, y) not in all_points and not np.isnan(z) and z > 0:
                        all_points[(x, y)] = z

        # Log summary of what we got
        valid_points = len(all_points)
        print(f"V Interpolated grid points: {valid_points} valid points from prioritized methods")

        return all_points

    def get_spline_grid_points(self):
        """
        Get interpolated grid points using only spline method
        Falls back to bilinear if spline is not available or has NaN values

        Returns:
            dict: Dictionary with (x,y) keys and z values from spline method
        """
        spline_points = {}

        print("DEBUG: Getting spline grid points...")
        print(f"DEBUG: interpolated_results keys: {list(self.interpolated_results.keys())}")

        # Try spline first
        if 'spline' in self.interpolated_results:
            print(f"DEBUG: Spline results found with {len(self.interpolated_results['spline'])} entries")
            for (x, y), z in self.interpolated_results['spline'].items():
                if not np.isnan(z) and z > 0:
                    spline_points[(x, y)] = z
                else:
                    print(f"DEBUG: Skipping spline point ({x:.3f}, {y:.3f}): z={z}, is_nan={np.isnan(z)}")
        else:
            print("DEBUG: Spline results not found in interpolated_results")

        print(f"DEBUG: Valid spline points collected: {len(spline_points)}")

        # If spline doesn't have enough points, fall back to bilinear
        if len(spline_points) < 4:
            print(f"DEBUG: Spline has only {len(spline_points)} points, trying bilinear fallback")
            if 'bilinear' in self.interpolated_results:
                print(f"DEBUG: Bilinear results found with {len(self.interpolated_results['bilinear'])} entries")
                for (x, y), z in self.interpolated_results['bilinear'].items():
                    if (x, y) not in spline_points and not np.isnan(z) and z > 0:
                        spline_points[(x, y)] = z
                print(f"DEBUG: After bilinear fallback: {len(spline_points)} points")
            else:
                print("DEBUG: Bilinear results not found either")

        valid_points = len(spline_points)
        print(f"V Spline grid points: {valid_points} valid points")

        return spline_points
    
    def log_interpolation_results(self):
        """
        Generate a detailed log of interpolation results for all methods.
        """
        log = "\n" + "="*80 + "\n"
        log += " " * 25 + "Z INTERPOLATION RESULTS\n"
        log += "="*80 + "\n"
        log += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        log += f"Original 3x3 grid -> Interpolated to {self.x_points_count}x{self.y_points_count} grid (FULL ROI COVERAGE)\n"
        log += f"Offset factor: {self.offset}\n"
        log += f"ROI boundaries: X{self.grbl_start[0]:.3f}→{self.grbl_end[0]:.3f}, Y{self.grbl_start[1]:.3f}→{self.grbl_end[1]:.3f}\n"
        log += "-"*80 + "\n"

        # Log original data
        log += "ORIGINAL 3x3 MAPPING DATA:\n"
        if len(self.x_original) == 3 and len(self.y_original) == 3:
            for i in range(3):
                row_str = ""
                for j in range(3):
                    x, y = self.x_original[j], self.y_original[i]
                    z = self.z_values.get((x, y), 0)
                    grid_num = i * 3 + j + 1
                    row_str += f"  {grid_num}:({x:.2f},{y:.2f})->Z={z:.4f} |"
                log += row_str[:-2] + "\n"
        log += f"\nOriginal points: {len(self.z_values)}\n"

        # Log results for each method
        for method in ['bilinear', 'cubic', 'spline']:
            if self.interpolated_z_grid.get(method) is None:
                continue

            log += "\n" + "-"*80 + "\n"
            log += f"INTERPOLATED GRID - METHOD: {method.upper()}\n"
            
            grid = self.interpolated_z_grid[method]
            for i in range(self.y_points_count):
                row_str = " ".join([f"{z:7.4f}" for z in grid[i, :]])
                log += f"  {row_str}\n"

            log += "\n" + f"STATISTICS ({method.upper()}):\n"
            z_values = grid[np.isfinite(grid) & (grid > 0)]
            if z_values.size > 0:
                log += f"  Min Z: {np.min(z_values):.4f}\n"
                log += f"  Max Z: {np.max(z_values):.4f}\n"
                log += f"  Mean Z: {np.mean(z_values):.4f}\n"
                log += f"  Std Z: {np.std(z_values):.4f}\n"
                log += f"  Z Range: {np.max(z_values) - np.min(z_values):.4f}\n"

        log += "="*80 + "\n"
        return log

    def _log_interpolation_results_safe(self):
        """
        Generate a detailed log of interpolation results without special characters.
        Safe for Windows encoding.

        Returns:
            str: Log string without special characters
        """
        log = "\n" + "="*80 + "\n"
        log += " " * 25 + "Z INTERPOLATION RESULTS\n"
        log += "="*80 + "\n"
        log += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        log += f"Original 3x3 grid -> Interpolated to {self.x_points_count}x{self.y_points_count} grid (FULL ROI COVERAGE)\n"
        log += f"Offset factor: {self.offset}\n"
        log += f"ROI boundaries: X{self.grbl_start[0]:.3f} to {self.grbl_end[0]:.3f}, Y{self.grbl_start[1]:.3f} to {self.grbl_end[1]:.3f}\n"
        log += "-"*80 + "\n"

        # Log original data (safe version without arrows)
        log += "ORIGINAL 3x3 MAPPING DATA:\n"
        if len(self.x_original) == 3 and len(self.y_original) == 3:
            for i in range(3):
                row_str = ""
                for j in range(3):
                    x, y = self.x_original[j], self.y_original[i]
                    z = self.z_values.get((x, y), 0)
                    grid_num = i * 3 + j + 1
                    row_str += f"  {grid_num}:(X{x:.2f},Y{y:.2f})=Z{z:.4f} |"
                log += row_str[:-2] + "\n"
        log += f"\nOriginal points: {len(self.z_values)}\n"

        # Log results for each method
        for method in ['bilinear', 'cubic', 'spline']:
            if self.interpolated_z_grid.get(method) is None:
                continue

            log += "\n" + "-"*80 + "\n"
            log += f"INTERPOLATED GRID - METHOD: {method.upper()}\n"

            grid = self.interpolated_z_grid[method]
            for i in range(self.y_points_count):
                row_str = " ".join([f"{z:7.4f}" for z in grid[i, :]])
                log += f"  {row_str}\n"

            log += "\n" + f"STATISTICS ({method.upper()}):\n"
            z_values = grid[np.isfinite(grid) & (grid > 0)]
            if z_values.size > 0:
                log += f"  Min Z: {np.min(z_values):.4f}\n"
                log += f"  Max Z: {np.max(z_values):.4f}\n"
                log += f"  Mean Z: {np.mean(z_values):.4f}\n"
                log += f"  Std Z: {np.std(z_values):.4f}\n"
                log += f"  Z Range: {np.max(z_values) - np.min(z_values):.4f}\n"
                log += f"  Valid points: {z_values.size}\n"

                # Add NaN count if any
                nan_count = np.sum(np.isnan(grid))
                if nan_count > 0:
                    log += f"  NaN values: {nan_count}\n"

        log += "="*80 + "\n"
        log += "NOTE: Use Z_Interpolation class to generate interpolated grid\n"
        log += "="*80 + "\n"
        return log

    def save_interpolation_data(self, filename=None):
        """
        Save interpolation data to file with UTF-8 encoding

        Args:
            filename: Optional filename, if None uses timestamp
        """
        if filename is None:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            filename = f"z_interpolation_{timestamp}.txt"

        try:
            # Use UTF-8 encoding to handle special characters like arrows
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_interpolation_results())
            print(f"V Interpolation data saved to: {filename}")
        except Exception as e:
            print(f"X Error saving interpolation data: {e}")
            # Fallback: try with safe logging (no special characters)
            try:
                safe_filename = filename.replace('.txt', '_safe.txt')
                with open(safe_filename, 'w', encoding='utf-8') as f:
                    f.write(self._log_interpolation_results_safe())
                print(f"V Interpolation data saved (safe mode) to: {safe_filename}")
            except Exception as e2:
                print(f"X Failed to save interpolation data: {e2}")
