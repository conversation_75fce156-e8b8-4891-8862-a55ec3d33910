import json
import os
from pathlib import Path
from typing import Any, Dict, Optional


def _deep_merge(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deep merge two dictionaries (override into base) without modifying inputs.
    Lists are replaced, dicts are merged recursively, scalars overwritten.
    """
    result = dict(base)
    for k, v in override.items():
        if k in result and isinstance(result[k], dict) and isinstance(v, dict):
            result[k] = _deep_merge(result[k], v)
        else:
            result[k] = v
    return result

DEFAULT_CONFIG: Dict[str, Any] = {
    "ui": {
        "window_title": "Slide Scanner",
        "video_container_width": 1050,
        "video_container_height": 850,
        "live_preview_popup_width": 240,
        "icon_size": 32
    },
    "camera": {
        "default_zoom": "10X",
        "zoom_levels": ["4X", "10X", "20X", "40X"],
        "ffc_files": {
            "4X": "ffc_4X.ffc",
            "10X": "ffc_10X.ffc",
            "20X": "ffc_40X.ffc",
            "40X": "ffc_100X.ffc"
        },
        "zoom_settings": {
            "4X": {
                "autoExp": 0,
                "expTime": 1705,
                "expGain": 100,
                "temp": 7582,
                "tint": 1033,
                "ffc_file": "ffc_4X.ffc"
            },
            "10X": {
                "autoExp": 0,
                "expTime": 1705,
                "expGain": 100,
                "temp": 7582,
                "tint": 1033,
                "ffc_file": "ffc_10X.ffc"
            },
            "20X": {
                "autoExp": 0,
                "expTime": 2526,
                "expGain": 100,
                "temp": 5322,
                "tint": 1001,
                "ffc_file": "ffc_40X.ffc"
            },
            "40X": {
                "autoExp": 0,
                "expTime": 20208,
                "expGain": 100,
                "temp": 5265,
                "tint": 989,
                "ffc_file": "ffc_100X.ffc"
            }
        },
        "preview_cam_index": 0,
        "fps_timer_ms": 1000
    },
    "icons": {
        "base_path": "Icons",
        "files": {
            "up": "up.png",
            "down": "down.png",
            "left": "left.png",
            "right": "right.png",
            "zup": "Dup.png",
            "zdown": "Ddown.png",
            "next": "next.png"
        }
    },
    "jogging": {
        "valid_keys": {
            "Key_Up": ["Y", 65],
            "Key_Down": ["Y", 0],
            "Key_Left": ["X", 0],
            "Key_Right": ["X", 75],
            "Key_PageUp": ["Z", 11],
            "Key_PageDown": ["Z", 0]
        }
    },
    "grbl": {
        "baudrate": 115200,
        "timeout": 1.0,
        "port_descriptions": ["Arduino", "CH340"],
        "status_poll_interval_ms": 250,
        "wait_idle_timeout_s": 30.0,
        "settings_file": "GRBL_Settings.json",
        "sample_coordinates": { "X": 45, "Y": 10, "Z": 9 },
        "poll_sleep_s": 0.1,
        "post_move_stabilize_s": 0.2
    },
    "autofocus": {
        "use_grbl_position": True,
        "z_smoothing_alpha": 0.4,
        "score_smoothing_alpha": 0.3,
        "early_stop_drop_pct": 0.10,
        "min_z_after_peak_mm": 0.005,
        "min_decrease_window": 3,
        "improvement_eps_pct": 0.005,
        "min_samples_before_stop": 5,
        "coarse": {
            "start_z": 9.0,
            "end_z": 10.5,
            "feed_rate_mmpm": 10.0
        },
        "fine": {
            "range_mm": 0.1,
            "feed_rate_mmpm": 1.0,
            "probe_delta": 0.03,
            "z_min": 0.0,
            "z_max": 11.0
        },
        "timer_interval_ms": 50
    },
    "stitching": {
        "overlap_percentage": 20,
        "capture_delay_s": 1.0,
        "output_directory": "Stitching_Results"
    },
    "mapping_af": {
        "grid_offset": 0.2,
        "interpolation_offset": 0.2
    }
}


class AppConfig:
    """
    Centralized application configuration loader with sane defaults and JSON override.
    - Loads defaults from DEFAULT_CONFIG in this module
    - Optionally overlays with config/app_config.json if present
    - Provides typed accessors and simple dotted-path get
    - Can write out a default config file if missing (to help customization)
    """

    def __init__(self, base_dir: Optional[Path] = None):
        self._base_dir = Path(base_dir) if base_dir else Path(__file__).parent
        self._config_dir = self._base_dir / "config"
        self._config_file = self._config_dir / "app_config.json"
        self._config: Dict[str, Any] = dict(DEFAULT_CONFIG)
        self._load()

    @property
    def path(self) -> Path:
        return self._config_file

    def _load(self) -> None:
        # Ensure directory exists
        os.makedirs(self._config_dir, exist_ok=True)

        # Load override if exists
        if self._config_file.exists():
            try:
                with self._config_file.open("r", encoding="utf-8") as f:
                    override = json.load(f)
                self._config = _deep_merge(DEFAULT_CONFIG, override)
            except Exception as e:
                # Fallback to defaults, but keep going
                print(f"[Config] Failed to load {self._config_file}: {e}. Using defaults.")
                self._config = dict(DEFAULT_CONFIG)
        else:
            # Persist defaults as a starting point for users to edit
            try:
                with self._config_file.open("w", encoding="utf-8") as f:
                    json.dump(DEFAULT_CONFIG, f, indent=2)
                self._config = dict(DEFAULT_CONFIG)
                print(f"[Config] Default config written to {self._config_file}")
            except Exception as e:
                print(f"[Config] Failed to write default config file: {e}")
                self._config = dict(DEFAULT_CONFIG)

    def reload(self) -> None:
        """Reload configuration from disk (keeps defaults)."""
        self._load()

    def save(self) -> None:
        """Save current in-memory config to disk."""
        try:
            with self._config_file.open("w", encoding="utf-8") as f:
                json.dump(self._config, f, indent=2)
        except Exception as e:
            print(f"[Config] Failed to save config: {e}")

    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Retrieve nested config using dotted path, e.g., "ui.icon_size".
        """
        parts = key_path.split(".")
        cur: Any = self._config
        for p in parts:
            if isinstance(cur, dict) and p in cur:
                cur = cur[p]
            else:
                return default
        return cur

    def as_dict(self) -> Dict[str, Any]:
        return dict(self._config)

    # Convenience helpers
    def icon_path(self, key: str) -> str:
        """
        Returns relative icon path like 'Icons/up.png' based on config icons.files.
        """
        base = self.get("icons.base_path", "Icons")
        fname = self.get(f"icons.files.{key}", f"{key}.png")
        return f"{base}/{fname}"

    def camera_zoom_levels(self):
        return list(self.get("camera.zoom_levels", []))

    def zoom_settings(self) -> Dict[str, Dict[str, Any]]:
        return dict(self.get("camera.zoom_settings", {}))


# Module-level shared config
config = AppConfig()