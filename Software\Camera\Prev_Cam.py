# File: preview.py

import cv2, os
from PyQt5.QtCore import QObject, QTimer, pyqtSlot, Qt
from PyQt5.QtGui import QImage, QPixmap
import numpy as np

# Default FFC file path
ffc_prev = "ffc_prev.npz"

class USBPreview(QObject):
    """
    USB camera preview driver that updates a QLabel with live frames.
    """
    def __init__(self, main_label, preview_label, cam_index=0, ffc_path=None, parent=None):
        super().__init__(parent)
        self.main_label = main_label
        self.preview_label = preview_label
        self.is_swapped = False
        self.extra_label = None
        self.flat_frame = None
        self.dark_frame = None
        self.ffc_enabled = False

        self.is_overridden = False
        self.override_pixmap = None

        # Gunakan default path jika tidak diberikan
        if ffc_path is None:
            ffc_path = os.path.join(os.path.dirname(__file__), ffc_prev)

        # OpenCV capture
        self.cap = cv2.VideoCapture(cam_index, cv2.CAP_MSMF)
        if not self.cap.isOpened():
            raise RuntimeError(f"Cannot open camera index {cam_index}")
        
        # Set resolusi ke 1920x1080
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)

        # Verifikasi resolusi yang sebenarnya diterima
        actual_width = self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        actual_height = self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)

        # Simpan resolusi aktual untuk referensi
        self.actual_width = int(actual_width)
        self.actual_height = int(actual_height)

        print("="*50)
        print("[INFO] VERIFIKASI RESOLUSI PREVIEW CAMERA:")
        print(f"  -> Diminta: 1920 x 1080")
        print(f"  -> Diberikan oleh Kamera: {self.actual_width} x {self.actual_height}")

        if self.actual_width != 1920 or self.actual_height != 1080:
            print(f"  -> WARNING: Resolusi tidak sesuai! ROI calculation mungkin tidak akurat.")
            print(f"  -> Akan menggunakan resolusi aktual untuk perhitungan koordinat.")
        else:
            print(f"  -> OK: Resolusi sesuai dengan yang diminta.")
        print("="*50)
        
        # Load FFC jika file tersedia
        if os.path.exists(ffc_path):
            try:
                data = np.load(ffc_path)
                self.flat_frame = data['flat']
                self.dark_frame = data['dark']
                self.ffc_enabled = True
                print(f"[INFO] FFC loaded from {ffc_path}")
            except Exception as e:
                print(f"[WARNING] Failed to load FFC: {e}")
        else:
            print(f"[INFO] No FFC file found at {ffc_path}, continuing without correction.")

        # Timer to grab frames
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_frame)
        self.start()
    
    def start(self):
        """Mulai timer untuk mengambil frame."""
        # Tentukan interval lagi atau simpan sebagai atribut self
        interval = int(1000 / 30) # Asumsi fps 30
        if not self.timer.isActive():
            self.timer.start(interval)
            print("sudah aktif")
        else:
            print("tidak aktif")

    def _update_frame(self):
        pixmap_to_show = None
        
        if self.is_overridden:
            # Jika dalam mode override, gunakan pixmap override
            pixmap_to_show = self.override_pixmap
        else:
            # Jika tidak, jalankan logika pengambilan frame normal
            ret, frame = self.cap.read()
            if not ret: return
        
            self.current_frame = frame.copy()

            # Convert BGR to RGB
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Terapkan FFC jika diaktifkan
            if self.ffc_enabled and self.flat_frame is not None and self.dark_frame is not None:
                try:
                    frame = (frame.astype(float) - self.dark_frame) / (self.flat_frame - self.dark_frame + 1e-6)
                    frame = np.clip(frame * 255, 0, 255).astype(np.uint8)
                except Exception as e:
                    print(f"[WARNING] FFC processing failed: {e}")

            h, w, ch = frame.shape
            bytes_per_line = ch * w
            qimg = QImage(frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            pixmap_to_show = QPixmap.fromImage(qimg)

        if pixmap_to_show and not pixmap_to_show.isNull():
            # Simpan original pixmap untuk ROI calculation yang akurat
            original_pixmap = pixmap_to_show.copy()

            # Verifikasi ukuran frame hanya sekali saat startup
            if not hasattr(self, '_frame_size_logged'):
                print(f"[INFO] Frame pixmap size: {original_pixmap.width()} x {original_pixmap.height()}")
                print(f"[INFO] Camera resolution: {self.actual_width} x {self.actual_height}")
                if original_pixmap.width() == self.actual_width and original_pixmap.height() == self.actual_height:
                    print(f"[INFO] Frame size matches camera resolution - ROI calculation will be accurate")
                else:
                    print(f"[WARNING] Frame size mismatch - ROI calculation may be inaccurate")
                self._frame_size_logged = True

            if self.is_swapped:
                if self.main_label and self.main_label.isVisible():
                    # Set original pixmap untuk ROI calculation
                    if hasattr(self.main_label, 'original_pixmap'):
                        self.main_label.original_pixmap = original_pixmap
                        # Set juga informasi resolusi aktual
                        if hasattr(self.main_label, 'actual_resolution'):
                            self.main_label.actual_resolution = (self.actual_width, self.actual_height)

                    # Scale untuk display
                    scaled_pixmap = pixmap_to_show.scaled(
                        self.main_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                    )
                    self.main_label.setPixmap(scaled_pixmap)
            else:
                if self.preview_label and self.preview_label.isVisible():
                    # Set original pixmap untuk ROI calculation
                    if hasattr(self.preview_label, 'original_pixmap'):
                        self.preview_label.original_pixmap = original_pixmap
                        # Set juga informasi resolusi aktual
                        if hasattr(self.preview_label, 'actual_resolution'):
                            self.preview_label.actual_resolution = (self.actual_width, self.actual_height)

                    # Scale untuk display
                    scaled_pixmap = pixmap_to_show.scaled(
                        self.preview_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                    )
                    self.preview_label.setPixmap(scaled_pixmap)

    def stop(self):
        """Stop preview and release camera."""
        self.timer.stop()

    def set_extra_label(self, label):
        self.extra_label = label
    
    @pyqtSlot(QPixmap)
    def set_override_image(self, pixmap):
        """Mengaktifkan mode override dan menetapkan gambar pengganti."""
        self.override_pixmap = pixmap
        self.is_overridden = True

    @pyqtSlot()
    def clear_override(self):
        """Menonaktifkan mode override dan kembali ke streaming normal."""
        self.is_overridden = False
        self.override_pixmap = None

    def swap_streams(self):
        self.is_swapped = not self.is_swapped
