"""
Stitching Worker <PERSON><PERSON>le
Handles the stitching process using interpolated Z values from Mapping AF
Optimized with asynchronous capture pipeline and RAM Disk staging
"""

import os
import time
import shutil
import numpy as np
import traceback
from datetime import datetime
from queue import Queue, Empty
from threading import Thread, Event
from io import BytesIO
import importlib.util
from PyQt5.QtCore import QObject, pyqtSignal, QBuffer, QIODevice
from PyQt5.QtGui import QImage, QImageWriter
import cv2


class StitchingWorker(QObject):
    """
    Worker class for handling stitching process
    """
    
    # Signals
    progress = pyqtSignal(int, str)  # progress percentage, status message
    log_message = pyqtSignal(str)    # log messages
    finished = pyqtSignal()          # finished signal
    error_occurred = pyqtSignal(str) # error signal
    stitching_result_ready = pyqtSignal(object)  # stitched image data
    
    def __init__(self, camera, grbl, z_interpolation, output_folder=None, overlap_x_percent=20, overlap_y_percent=10):
        """
        Initialize StitchingWorker

        Args:
            camera: Camera instance for capturing images
            grbl: GRBL instance for movement control
            z_interpolation: ZInterpolation object with interpolated grid points
            output_folder: Optional custom output folder path
            overlap_x_percent: Overlap percentage for X direction (default 20%)
            overlap_y_percent: Overlap percentage for Y direction (default 10%)
        """
        super().__init__()

        self.camera = camera
        self.grbl = grbl
        self.z_interpolation = z_interpolation
        self.output_folder = output_folder

        # Overlap parameters for final stitching
        self.overlap_x_percent = overlap_x_percent
        self.overlap_y_percent = overlap_y_percent

        # Stitching parameters
        self.current_point = 0
        self.total_points = 0
        self.grid_points = []
        self.is_running = False
        self.should_stop = False
        self.last_known_position = (0, 0, 0)
        self.timing_stats = {}

        # Single bilinear stitching (user requested only bilinear, no fixed/spline)
        self.current_run = 1
        self.total_runs = 1  # Changed from 3 to 1
        self.center_z_value = None  # Will store the center Z from mapping AF
        self.interpolated_grid_points = []  # Store bilinear interpolated points
        # Removed fixed_z_grid_points and spline_grid_points

        # Extract center Z value from mapping results
        self._extract_center_z_value()
        
        # Movement parameters
        self.movement_feedrate = 1000  # mm/min
        self.z_feedrate = 500         # mm/min for Z movements
        self.settle_time = 0.05       # seconds to wait after movement
        self.capture_delay = 0.01     # seconds to wait before capture

        # Optimized capture parameters
        self.use_memory_buffer = True  # Always use in-memory buffer
        self.staging_format = "PNG_FAST"  # PNG with minimal compression for staging
        self.final_format = "PNG"        # Final output format
        self.final_output_root = r"D:\Stitch"  # Save final results to D: drive

        # Asynchronous capture pipeline
        self._save_queue = None
        self._writer_thread = None
        self._writer_stop_event = None
        self._memory_buffer = {}  # In-memory buffer: {filename: bytes_data}
        self._files_staged = []   # Track staged files for final copy

        # Metadata collection for final JSON output
        self.capture_metadata = []  # List to collect metadata during capture
        self.metadata_filename = None  # Will be set during finalization

    def _extract_center_z_value(self):
        """
        Extract the center Z value from mapping AF results.
        The center point is the one that used 'full' AF in the 3x3 grid.
        """
        try:
            if not hasattr(self.z_interpolation, 'mapping_results') or not self.z_interpolation.mapping_results:
                self.log_message.emit("⚠️ Warning: No mapping results available for center Z extraction")
                return

            # Calculate center coordinates from grbl_start and grbl_end
            x_start, y_start = self.z_interpolation.grbl_start
            x_end, y_end = self.z_interpolation.grbl_end
            center_x = (x_start + x_end) / 2
            center_y = (y_start + y_end) / 2

            # Look for the center point in mapping results
            center_z = None
            min_distance = float('inf')

            for (x, y), z_value in self.z_interpolation.mapping_results.items():
                distance = ((x - center_x)**2 + (y - center_y)**2)**0.5
                if distance < min_distance and z_value > 0:
                    min_distance = distance
                    center_z = z_value

            if center_z is not None:
                self.center_z_value = center_z
                self.log_message.emit(f"✓ Center Z value extracted: {center_z:.4f} (from full AF at center point)")
            else:
                self.log_message.emit("⚠️ Warning: Could not find valid center Z value from mapping results")

        except Exception as e:
            self.log_message.emit(f"Error extracting center Z value: {e}")

    def _estimate_memory_usage(self):
        """
        Estimate memory usage for in-memory buffer

        Returns:
            tuple: (estimated_mb_per_image, total_estimated_mb)
        """
        try:
            # Estimate based on typical image size
            # Assume 1920x1080 RGB image compressed to PNG
            estimated_bytes_per_image = 6 * 1024 * 1024  # ~6MB per PNG (conservative estimate)

            if hasattr(self, 'interpolated_grid_points'):
                # For single bilinear stitching
                total_images = len(self.interpolated_grid_points)
            else:
                total_images = 100  # Default estimate

            total_mb = (estimated_bytes_per_image * total_images) / (1024 * 1024)
            mb_per_image = estimated_bytes_per_image / (1024 * 1024)

            return mb_per_image, total_mb
        except Exception:
            return 6.0, 600.0  # Default conservative estimates

    def _setup_capture_pipeline(self):
        """
        Setup asynchronous capture pipeline with in-memory buffer
        """
        try:
            # Estimate memory usage
            mb_per_image, total_mb = self._estimate_memory_usage()

            # Initialize memory buffer
            if self.use_memory_buffer:
                self._memory_buffer = {}
                self.log_message.emit(f"✓ In-memory buffer enabled")
                self.log_message.emit(f"  Estimated usage: {mb_per_image:.1f}MB per image, {total_mb:.1f}MB total")
            else:
                self.log_message.emit("⚠️ Memory buffer disabled, using direct save")

            # Setup queue and worker thread
            self._save_queue = Queue(maxsize=50)  # Buffer up to 50 images
            self._writer_stop_event = Event()
            self._files_staged = []

            # Start background writer thread
            self._writer_thread = Thread(target=self._writer_loop, daemon=True)
            self._writer_thread.start()

            self.log_message.emit("✓ Asynchronous capture pipeline initialized")
            return True

        except Exception as e:
            self.log_message.emit(f"Error setting up capture pipeline: {e}")
            return False

    def _writer_loop(self):
        """
        Background thread loop for writing images to staging area
        """
        while not self._writer_stop_event.is_set():
            try:
                # Get item from queue with timeout
                try:
                    item = self._save_queue.get(timeout=1.0)
                except Empty:
                    continue

                if item is None:  # Poison pill to stop
                    break

                frame, filename, run_number, x, y, z = item

                # Save to memory buffer or direct to disk
                if self.use_memory_buffer:
                    # Encode to PNG in memory
                    success, png_data = self._encode_frame_to_memory(frame)
                    if success:
                        # Store in memory buffer
                        self._memory_buffer[filename] = png_data
                        self._files_staged.append(filename)
                    else:
                        self.log_message.emit(f"⚠️ Failed to encode to memory: {filename}")
                else:
                    # Direct save to output folder
                    output_path = os.path.join(self.output_folder, filename)
                    success = self._save_frame_to_disk(frame, output_path)
                    if not success:
                        self.log_message.emit(f"⚠️ Failed to save: {filename}")

                self._save_queue.task_done()

            except Exception as e:
                self.log_message.emit(f"Error in writer loop: {e}")
                if not self._save_queue.empty():
                    self._save_queue.task_done()

    def _encode_frame_to_memory(self, frame):
        """
        Encode frame to PNG in memory buffer

        Args:
            frame: numpy array (H, W, 3) RGB

        Returns:
            tuple: (success: bool, png_data: bytes or None)
        """
        try:
            height, width, channels = frame.shape
            if channels != 3:
                return False, None

            # Create QImage
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)

            if q_image.isNull():
                return False, None

            # Encode to PNG in memory using QBuffer
            buffer = QBuffer()
            buffer.open(QIODevice.WriteOnly)
            writer = QImageWriter(buffer, b"PNG")
            writer.setCompression(1)  # Minimal compression for speed
            success = writer.write(q_image)

            if success:
                png_data = buffer.data().data()  # Get bytes from QByteArray
                buffer.close()
                return True, png_data
            else:
                buffer.close()
                return False, None

        except Exception as e:
            self.log_message.emit(f"Error encoding frame to memory: {e}")
            return False, None

    def _save_frame_to_disk(self, frame, file_path):
        """
        Save frame directly to disk (fallback method)

        Args:
            frame: numpy array (H, W, 3) RGB
            file_path: full path to output file

        Returns:
            bool: True if successful
        """
        try:
            height, width, channels = frame.shape
            if channels != 3:
                return False

            # Create QImage
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)

            if q_image.isNull():
                return False

            # Ensure output directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Save with optimized PNG settings
            writer = QImageWriter(file_path, b"PNG")
            writer.setCompression(1)  # Minimal compression for speed
            success = writer.write(q_image)

            return success and os.path.exists(file_path) and os.path.getsize(file_path) > 1000

        except Exception as e:
            self.log_message.emit(f"Error saving frame to disk: {e}")
            return False

    def _finalize_capture_pipeline(self):
        """
        Finalize capture pipeline: wait for queue to empty, copy files, cleanup
        """
        try:
            finalize_start_time = time.time()

            # Signal writer thread to stop and wait for queue to empty
            if self._save_queue is not None:
                self.log_message.emit("Waiting for capture queue to finish...")
                self._save_queue.join()  # Wait for all items to be processed

                # Stop writer thread
                self._save_queue.put(None)  # Poison pill
                self._writer_stop_event.set()

                if self._writer_thread and self._writer_thread.is_alive():
                    self._writer_thread.join(timeout=10)

            # Write files from memory buffer to final destination
            if self.use_memory_buffer and self._files_staged:
                self.log_message.emit(f"Writing {len(self._files_staged)} files from memory buffer to D: drive...")
                copy_start_time = time.time()

                # Write all files to final destination
                files_written = 0
                for filename in self._files_staged:
                    try:
                        final_path = os.path.join(self.output_folder, filename)

                        # Get PNG data from memory buffer
                        if filename in self._memory_buffer:
                            png_data = self._memory_buffer[filename]

                            # Ensure output directory exists
                            os.makedirs(os.path.dirname(final_path), exist_ok=True)

                            # Write binary data to file
                            with open(final_path, 'wb') as f:
                                f.write(png_data)

                            # Verify final file
                            if os.path.exists(final_path) and os.path.getsize(final_path) > 1000:
                                files_written += 1
                            else:
                                self.log_message.emit(f"⚠️ Final file verification failed: {filename}")
                        else:
                            self.log_message.emit(f"⚠️ File not found in memory buffer: {filename}")

                    except Exception as e:
                        self.log_message.emit(f"Error writing {filename}: {e}")

                copy_time = time.time() - copy_start_time
                self.timing_stats['copy_time'] = copy_time
                self.log_message.emit(f"✓ File write completed in {copy_time:.2f} seconds")
                self.log_message.emit(f"✓ {files_written} individual images saved to disk")

                # Save metadata before stitching
                self.log_message.emit("📄 Saving stitching metadata...")
                metadata_saved = self._save_metadata_file()
                if metadata_saved:
                    self.log_message.emit("✓ Metadata saved successfully")
                else:
                    self.log_message.emit("⚠️ Metadata save failed, continuing with stitching")

                # Perform final stitching using ImageStitcher from Stitch.py
                self.log_message.emit("🔄 Starting final image stitching...")
                stitch_start_time = time.time()

                try:
                    stitched_image = self._perform_final_stitching()
                    if stitched_image is not None:
                        # Save stitched result
                        self._save_stitched_result(stitched_image)
                        stitch_time = time.time() - stitch_start_time
                        self.timing_stats['stitch_time'] = stitch_time
                        self.log_message.emit(f"✓ Final stitching completed in {stitch_time:.2f} seconds")

                        # Emit signal with stitched result for UI display
                        self.stitching_result_ready.emit(stitched_image)
                        self.log_message.emit("📤 Stitched result sent to UI for display")
                        stitching_success = True
                    else:
                        self.log_message.emit("❌ Final stitching failed!")
                        stitching_success = False
                except Exception as e:
                    self.log_message.emit(f"❌ Critical error during stitching: {e}")
                    stitching_success = False

                # Cleanup memory buffer
                try:
                    buffer_size_mb = sum(len(data) for data in self._memory_buffer.values()) / (1024 * 1024)
                    self._memory_buffer.clear()
                    self.log_message.emit(f"✓ Memory buffer cleared ({buffer_size_mb:.1f}MB freed)")
                except Exception as e:
                    self.log_message.emit(f"Warning: Could not cleanup memory buffer: {e}")

                # Return stitching success status
                return stitching_success

            finalize_time = time.time() - finalize_start_time
            self.timing_stats['finalize_time'] = finalize_time
            self.log_message.emit(f"✓ Capture pipeline finalized in {finalize_time:.2f} seconds")

        except Exception as e:
            self.log_message.emit(f"Error finalizing capture pipeline: {e}")

    def _convert_bmp_to_png(self, bmp_path, png_path):
        """
        Convert BMP file to PNG with high quality compression

        Args:
            bmp_path: source BMP file path
            png_path: destination PNG file path
        """
        try:
            # Load BMP and save as PNG
            q_image = QImage(bmp_path)
            if not q_image.isNull():
                writer = QImageWriter(png_path, b"PNG")
                writer.setCompression(6)  # Good compression for final files
                return writer.write(q_image)
            return False
        except Exception as e:
            self.log_message.emit(f"Error converting BMP to PNG: {e}")
            return False

    def _cleanup_capture_pipeline(self):
        """
        Emergency cleanup of capture pipeline resources
        """
        try:
            if self._writer_stop_event:
                self._writer_stop_event.set()

            if self._writer_thread and self._writer_thread.is_alive():
                self._writer_thread.join(timeout=5)

            # Cleanup memory buffer
            if self.use_memory_buffer and self._memory_buffer:
                try:
                    self._memory_buffer.clear()
                except Exception:
                    pass  # Best effort cleanup

        except Exception:
            pass  # Silent cleanup

    def prepare_stitching(self):
        """
        Prepare stitching by getting all interpolated grid points and creating output folder

        Returns:
            bool: True if preparation successful, False otherwise
        """
        try:
            # Safety check: Validate camera connection
            if self.camera is None:
                self.error_occurred.emit("Camera not available")
                return False

            if not hasattr(self.camera, 'is_running') or not self.camera.is_running:
                self.error_occurred.emit("Camera is not running")
                return False

            # Safety check: Validate GRBL connection
            if self.grbl is None:
                self.error_occurred.emit("GRBL not available")
                return False

            if not hasattr(self.grbl, 'grbl') or not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL is not connected")
                return False

            # Get all interpolated points
            if self.z_interpolation is None:
                self.error_occurred.emit("Z interpolation data not available")
                return False

            all_points = self.z_interpolation.get_interpolated_grid_points()
            if not all_points:
                self.error_occurred.emit("No interpolated points available")
                return False

            # Safety check: Validate interpolated points
            if len(all_points) < 4:
                self.error_occurred.emit(f"Too few interpolated points ({len(all_points)}). Need at least 4 points.")
                return False

            # Additional validation: Check for NaN values
            nan_count = sum(1 for z in all_points.values() if np.isnan(z))
            if nan_count > 0:
                self.error_occurred.emit(f"Found {nan_count} NaN values in interpolated data. This should not happen with the new filtering.")
                return False

            # Check for invalid Z values
            invalid_count = sum(1 for z in all_points.values() if z <= 0)
            if invalid_count > 0:
                self.log_message.emit(f"Warning: Found {invalid_count} invalid Z values (<=0) in interpolated data")

            # Prepare interpolated Z grid points for single bilinear stitching
            self.interpolated_grid_points = self._create_efficient_path(all_points)
            self.log_message.emit(f"✓ Prepared single bilinear stitching: {len(self.interpolated_grid_points)} points")

            # Set initial grid points for first run
            self.grid_points = self.interpolated_grid_points
            self.total_points = len(self.grid_points)

            # Safety check: Validate coordinate ranges
            if not self._validate_coordinate_ranges():
                return False

            # Validate path distances once at the beginning
            self._validate_path_distances()

            self.log_message.emit(f"Prepared {self.total_points} points for single bilinear stitching")

            # Create output folder
            if not self._create_output_folder():
                return False

            # Setup optimized capture pipeline
            if not self._setup_capture_pipeline():
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Error preparing stitching: {e}")
            return False
    
    def _create_efficient_path(self, points_dict):
        """
        Create an efficient movement path through all grid points
        Uses zigzag pattern to minimize travel distance
        
        Args:
            points_dict: Dictionary with (x,y) keys and z values
            
        Returns:
            list: List of (x, y, z) tuples in optimal order
        """
        # Convert to list of tuples
        points_list = [(x, y, z) for (x, y), z in points_dict.items()]
        
        # Group by Y coordinate and sort
        y_groups = {}
        for x, y, z in points_list:
            if y not in y_groups:
                y_groups[y] = []
            y_groups[y].append((x, y, z))
        
        # Sort Y coordinates
        sorted_y = sorted(y_groups.keys())
        
        # Create zigzag pattern
        efficient_path = []
        reverse = False
        
        for y in sorted_y:
            row_points = sorted(y_groups[y], key=lambda p: p[0])  # Sort by X
            
            if reverse:
                row_points.reverse()
            
            efficient_path.extend(row_points)
            reverse = not reverse  # Alternate direction for next row
        
        self.log_message.emit(f"Created efficient zigzag path with {len(efficient_path)} points")
        return efficient_path

    def _validate_coordinate_ranges(self):
        """
        Validate that all coordinates are within safe ranges

        Returns:
            bool: True if all coordinates are safe, False otherwise
        """
        try:
            # Define safety limits (adjust based on your machine)
            X_MIN, X_MAX = -5.0, 50.0   # mm
            Y_MIN, Y_MAX = -5.0, 50.0   # mm
            Z_MIN, Z_MAX = 0.0, 25.0    # mm

            for x, y, z in self.grid_points:
                if not (X_MIN <= x <= X_MAX):
                    self.error_occurred.emit(f"X coordinate {x:.3f} is outside safe range [{X_MIN}, {X_MAX}]")
                    return False

                if not (Y_MIN <= y <= Y_MAX):
                    self.error_occurred.emit(f"Y coordinate {y:.3f} is outside safe range [{Y_MIN}, {Y_MAX}]")
                    return False

                if not (Z_MIN <= z <= Z_MAX):
                    self.error_occurred.emit(f"Z coordinate {z:.4f} is outside safe range [{Z_MIN}, {Z_MAX}]")
                    return False

            self.log_message.emit("All coordinates are within safe ranges")
            return True

        except Exception as e:
            self.error_occurred.emit(f"Error validating coordinates: {e}")
            return False

    def _validate_path_distances(self):
        """
        Validate that the distance between consecutive points is within a safe limit.
        This is a check on the generated path, not real-time movement.
        """
        max_step_distance = 10.0  # mm
        if len(self.grid_points) < 2:
            return  # Not enough points to check distance

        for i in range(len(self.grid_points) - 1):
            p1 = self.grid_points[i]
            p2 = self.grid_points[i+1]
            
            distance = ((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)**0.5
            
            if distance > max_step_distance:
                self.log_message.emit(
                    f"Warning: Large distance between point {i} ({p1[0]:.3f}, {p1[1]:.3f}) "
                    f"and point {i+1} ({p2[0]:.3f}, {p2[1]:.3f}). Distance: {distance:.3f}mm"
                )
        self.log_message.emit("Path distance validation complete.")

    def _create_output_folder(self):
        """
        Create output folder for stitching images on D: drive

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.output_folder is None:
                # Create folder with timestamp for single bilinear stitching on D: drive
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                folder_name = f"bilinear_stitching_{timestamp}"

                # Use D: drive for final output
                self.output_folder = os.path.join(self.final_output_root, folder_name)

            # Ensure D: drive folder exists
            os.makedirs(self.output_folder, exist_ok=True)

            # Test write capability to D: drive
            test_file = os.path.join(self.output_folder, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)

            # Create info file about the stitching process
            info_file = os.path.join(self.output_folder, "stitching_info.txt")
            with open(info_file, 'w') as f:
                f.write("SINGLE BILINEAR STITCHING PROCESS\n")
                f.write("="*50 + "\n")
                f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Process: Single bilinear stitching with configurable overlap\n")
                f.write(f"Overlap X: {self.overlap_x_percent}%\n")
                f.write(f"Overlap Y: {self.overlap_y_percent}%\n")
                f.write("\nFile Naming Convention:\n")
                f.write("stitch_bilinear_p###_X###.###_Y###.###_Z##.####_HHMMSS.png - Individual images\n")
                f.write("final_stitched_XXxYY_YYYYMMDD_HHMMSS.png - Final stitched result\n")
                f.write("="*50 + "\n")

            self.log_message.emit(f"Output folder created: {self.output_folder}")
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"Error creating output folder: {e}")
            return False
    
    def run(self):
        """
        Main stitching process
        """
        try:
            self.is_running = True
            self.should_stop = False
            self.timing_stats = {
                'start_time': time.time(),
                'preparation_time': 0,
                'total_movement_time': 0,
                'total_capture_time': 0,
                'finalize_time': 0,
                'copy_time': 0,
                'total_time': 0
            }
            
            self.log_message.emit("Starting stitching process...")
            
            # Prepare stitching
            prep_start_time = time.time()
            if not self.prepare_stitching():
                self.finished.emit()
                return
            self.timing_stats['preparation_time'] = time.time() - prep_start_time
            
            # Disable GRBL polling for stable operation
            if hasattr(self.grbl, 'stop_polling'):
                self.log_message.emit("[STITCHING] Disabling GRBL polling for stable operation")
                self.grbl.stop_polling()

            # Validate all grid points are ready before starting
            self.log_message.emit("DEBUG: Validating grid points before stitching...")
            self.log_message.emit(f"DEBUG: total_runs = {self.total_runs}")
            self.log_message.emit(f"DEBUG: interpolated_grid_points = {len(self.interpolated_grid_points) if self.interpolated_grid_points else 0}")

            # Run single bilinear stitching process
            if not self._validate_system_state():
                self.finished.emit()
                return

            self.log_message.emit(f"\n{'='*60}")
            self.log_message.emit(f"STARTING SINGLE BILINEAR STITCHING PROCESS")
            self.log_message.emit(f"Grid Points: {len(self.grid_points)}")
            self.log_message.emit(f"Final Overlap: X={self.overlap_x_percent}%, Y={self.overlap_y_percent}%")
            self.log_message.emit(f"{'='*60}")

            # Process each point
            for i, (x, y, z) in enumerate(self.grid_points):
                if not self._validate_system_state():
                    break

                self.current_point = i
                # Calculate progress
                overall_progress = int((i + 1) / self.total_points * 100)
                self.progress.emit(overall_progress, f"Stitching: Point {i+1}/{self.total_points}")
                self.log_message.emit(f"[STITCHING] Moving to point {i+1}/{self.total_points}: X={x:.3f}, Y={y:.3f}, Z={z:.4f}")

                # Move to position
                move_start_time = time.time()
                if not self._move_to_position(x, y, z):
                    self.error_occurred.emit(f"Failed to move to position ({x:.3f}, {y:.3f}, {z:.4f})")
                    self._emergency_stop()
                    break
                self.timing_stats['total_movement_time'] += time.time() - move_start_time

                if not self._validate_system_state():
                    break

                # Capture and save image
                capture_start_time = time.time()
                self.log_message.emit(f"DEBUG: Capturing image for point {i+1}")
                if not self._capture_and_save_image(x, y, z, i, 1):  # Always use run_number=1 for single run
                    self.error_occurred.emit(f"Failed to capture image at position ({x:.3f}, {y:.3f}, {z:.4f})")
                    continue
                capture_time = time.time() - capture_start_time
                self.timing_stats['total_capture_time'] += capture_time
                self.log_message.emit(f"DEBUG: Image captured and queued in {capture_time:.3f}s")

            if not self.should_stop:
                self.log_message.emit(f"✓ Single bilinear stitching process completed successfully")

            if not self.should_stop:
                self.progress.emit(100, "Stitching completed successfully")
                self.log_message.emit(f"\n{'='*60}")
                self.log_message.emit("STITCHING PROCESS COMPLETED SUCCESSFULLY")
                self.log_message.emit(f"{'='*60}")
                self.log_message.emit(f"✓ Bilinear stitching with final overlap parameters")
                self.log_message.emit(f"✓ Overlap X: {self.overlap_x_percent}%, Y: {self.overlap_y_percent}%")
                self.log_message.emit(f"✓ Final stitched image saved to: {self.output_folder}")
                self.log_message.emit(f"{'='*60}")
            
        except Exception as e:
            self.error_occurred.emit(f"Error during stitching: {e}")
        finally:
            # Finalize capture pipeline (copy files from RAM Disk, cleanup)
            stitching_success = False
            try:
                stitching_success = self._finalize_capture_pipeline()
            except Exception as e:
                self.log_message.emit(f"Error during pipeline finalization: {e}")

            self.timing_stats['total_time'] = time.time() - self.timing_stats['start_time']
            self._log_timing_summary()

            # Re-enable GRBL polling
            if hasattr(self.grbl, 'start_polling'):
                self.log_message.emit("[STITCHING] Re-enabling GRBL polling")
                self.grbl.start_polling()

            # Emergency cleanup
            self._cleanup_capture_pipeline()

            self.is_running = False

            # Emit finished signal with stitching success status
            self.finished.emit()
            if stitching_success:
                self.log_message.emit("🎉 STITCHING PROCESS COMPLETED SUCCESSFULLY!")
            else:
                self.log_message.emit("⚠️ STITCHING PROCESS COMPLETED WITH ERRORS")
    
    def _move_to_position(self, x, y, z):
        """
        Move to specified XYZ position with safety checks

        Args:
            x, y, z: Target coordinates

        Returns:
            bool: True if movement successful
        """
        try:
            # Safety check: Verify GRBL is still connected
            if not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL connection lost during movement")
                return False

            # Move XY first with timeout
            self.log_message.emit(f"Moving XY to ({x:.3f}, {y:.3f})")
            self.grbl.move_to(x, y)

            if not self.grbl.wait_for_idle(timeout_seconds=30):
                self.error_occurred.emit("XY movement timeout - stopping for safety")
                return False

            # Verify XY position reached (optional for testing)
            try:
                new_x, new_y, _ = self.grbl.get_current_position()
                xy_tolerance = 0.5  # mm - more lenient tolerance
                distance_error = ((new_x - x)**2 + (new_y - y)**2)**0.5

                if distance_error > xy_tolerance:
                    self.log_message.emit(f"XY position warning: Expected ({x:.3f}, {y:.3f}), got ({new_x:.3f}, {new_y:.3f}), error: {distance_error:.3f}mm")
                    # Don't fail for small position errors in testing
                else:
                    self.log_message.emit(f"XY position verified: ({new_x:.3f}, {new_y:.3f})")
            except Exception as e:
                self.log_message.emit(f"Warning: Could not verify XY position: {e}")

            # Move Z with timeout
            self.log_message.emit(f"Moving Z to {z:.4f}")
            self.grbl.move_to_z(z)

            if not self.grbl.wait_for_idle(timeout_seconds=15):
                self.error_occurred.emit("Z movement timeout - stopping for safety")
                return False

            # Verify Z position reached (optional for testing)
            try:
                _, _, new_z = self.grbl.get_current_position()
                z_tolerance = 0.1  # mm - more lenient tolerance
                z_error = abs(new_z - z)

                if z_error > z_tolerance:
                    self.log_message.emit(f"Z position warning: Expected {z:.4f}, got {new_z:.4f}, error: {z_error:.4f}mm")
                    # Don't fail for small position errors in testing
                else:
                    self.log_message.emit(f"Z position verified: {new_z:.4f}")
            except Exception as e:
                self.log_message.emit(f"Warning: Could not verify Z position: {e}")

            # Wait for settling
            time.sleep(self.settle_time)

            self.log_message.emit(f"Successfully moved to ({x:.3f}, {y:.3f}, {z:.4f})")
            self.last_known_position = (x, y, z)
            return True

        except Exception as e:
            self.error_occurred.emit(f"Critical error during movement: {e}")
            return False
    
    def _capture_and_save_image(self, x, y, z, point_index, run_number=1):
        """
        Capture image from camera and queue for asynchronous saving

        Args:
            x, y, z: Current coordinates
            point_index: Index of current point
            run_number: Current run number (1 for interpolated Z, 2 for fixed Z)

        Returns:
            bool: True if capture successful
        """
        try:
            # Safety check: Verify camera is still running
            if not hasattr(self.camera, 'is_running') or not self.camera.is_running:
                self.error_occurred.emit("Camera is not running during capture")
                return False

            # Wait before capture for image stabilization
            time.sleep(self.capture_delay)

            # Get frame from camera
            frame = None
            if hasattr(self.camera, '_last_numpy_frame') and self.camera._last_numpy_frame is not None:
                frame = self.camera._last_numpy_frame.copy()
            else:
                self.log_message.emit("Waiting for camera frame...")
                time.sleep(0.1)  # Short wait if frame not immediately available
                if hasattr(self.camera, '_last_numpy_frame') and self.camera._last_numpy_frame is not None:
                    frame = self.camera._last_numpy_frame.copy()

            if frame is None:
                self.error_occurred.emit("No frame available from camera")
                return False

            # Validate frame dimensions
            if len(frame.shape) != 3:
                self.error_occurred.emit(f"Invalid frame shape: {frame.shape}")
                return False

            height, width, channels = frame.shape
            if channels != 3:
                self.error_occurred.emit(f"Expected 3 channels, got {channels}")
                return False

            # Create filename with coordinates and timestamp (single bilinear run)
            timestamp = datetime.now().strftime("%H%M%S")
            filename = f"stitch_bilinear_p{point_index:03d}_X{x:.3f}_Y{y:.3f}_Z{z:.4f}_{timestamp}.png"

            # Collect metadata for this capture
            metadata_entry = {
                "filename": filename,
                "x": float(x),
                "y": float(y),
                "z": float(z),
                "point_index": point_index,
                "timestamp": datetime.now().isoformat(),
                "grid_position": self._calculate_grid_position(point_index)
            }
            self.capture_metadata.append(metadata_entry)

            # Queue frame for asynchronous saving (FAST - no I/O blocking)
            if self._save_queue is not None:
                try:
                    # Put item in queue - this should be very fast
                    item = (frame.copy(), filename, run_number, x, y, z)
                    self._save_queue.put(item, timeout=1.0)  # 1 second timeout

                    # Log immediate success (actual save happens in background)
                    self.log_message.emit(f"✓ Queued for save: {filename}")
                    return True

                except Exception as e:
                    self.error_occurred.emit(f"Failed to queue image for saving: {e}")
                    return False
            else:
                # Fallback to synchronous save if pipeline not available
                return self._save_image_synchronous(frame, filename, x, y, z)

        except Exception as e:
            self.error_occurred.emit(f"Critical error capturing image: {e}")
            return False

    def _save_image_synchronous(self, frame, filename, x, y, z):
        """
        Fallback synchronous save method (used when pipeline not available)

        Args:
            frame: numpy array (H, W, 3) RGB
            filename: target filename
            x, y, z: coordinates for logging

        Returns:
            bool: True if successful
        """
        try:
            height, width, channels = frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888)

            if q_image.isNull():
                self.error_occurred.emit("Failed to create QImage from frame")
                return False

            filepath = os.path.join(self.output_folder, filename)
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # Save image with error checking
            if q_image.save(filepath, "PNG"):
                # Verify file was actually created and has reasonable size
                if os.path.exists(filepath) and os.path.getsize(filepath) > 1000:  # At least 1KB
                    self.log_message.emit(f"✓ Saved (sync): {filename} ({os.path.getsize(filepath)} bytes)")
                    return True
                else:
                    self.error_occurred.emit(f"File saved but appears corrupted: {filename}")
                    return False
            else:
                self.error_occurred.emit(f"Failed to save image: {filename}")
                return False

        except Exception as e:
            self.error_occurred.emit(f"Error in synchronous save: {e}")
            return False
    
    def stop(self):
        """
        Stop the stitching process safely
        """
        self.should_stop = True
        self.log_message.emit("Emergency stop requested - stopping stitching process...")

        # Emergency stop GRBL movement
        try:
            if self.grbl and hasattr(self.grbl, 'stop_jog'):
                self.grbl.stop_jog()
                self.log_message.emit("GRBL movement stopped")
        except Exception as e:
            self.log_message.emit(f"Error stopping GRBL: {e}")

        # Stop capture pipeline
        try:
            self._cleanup_capture_pipeline()
        except Exception as e:
            self.log_message.emit(f"Error stopping capture pipeline: {e}")

    def _emergency_stop(self):
        """
        Emergency stop function for critical situations
        """
        try:
            self.should_stop = True
            self.error_occurred.emit("EMERGENCY STOP ACTIVATED")

            # Stop all GRBL movement immediately
            if self.grbl and hasattr(self.grbl, 'grbl') and self.grbl.grbl.is_open:
                # Send emergency stop command
                self.grbl.grbl.write(b'\x85')  # Real-time command for jog cancel
                self.grbl.grbl.write(b'!')     # Feed hold
                self.log_message.emit("Emergency stop commands sent to GRBL")

        except Exception as e:
            self.log_message.emit(f"Error during emergency stop: {e}")

    def _validate_system_state(self):
        """
        Validate system state before continuing with next point

        Returns:
            bool: True if system is in good state, False otherwise
        """
        try:
            # Check GRBL connection
            if not self.grbl.grbl.is_open:
                self.error_occurred.emit("GRBL connection lost")
                return False

            # Check camera state
            if not self.camera.is_running:
                self.error_occurred.emit("Camera stopped running")
                return False

            # Check if stop was requested
            if self.should_stop:
                self.log_message.emit("Stop requested by user")
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Error validating system state: {e}")
            return False

    def _log_timing_summary(self):
        """
        Log a summary of the timing statistics for the stitching process.
        """
        self.log_message.emit("--- SINGLE BILINEAR STITCHING TIMING SUMMARY ---")
        self.log_message.emit(f"Process: Bilinear stitching with final overlap parameters")
        self.log_message.emit(f"Overlap X: {self.overlap_x_percent}%, Y: {self.overlap_y_percent}%")

        # Detailed timing breakdown
        prep_time = self.timing_stats.get('preparation_time', 0)
        move_time = self.timing_stats.get('total_movement_time', 0)
        capture_time = self.timing_stats.get('total_capture_time', 0)
        finalize_time = self.timing_stats.get('finalize_time', 0)
        copy_time = self.timing_stats.get('copy_time', 0)
        stitch_time = self.timing_stats.get('stitch_time', 0)
        total_time = self.timing_stats.get('total_time', 0)

        self.log_message.emit(f"Preparation Time: {prep_time:.2f} seconds")
        self.log_message.emit(f"Total Movement Time: {move_time:.2f} seconds")
        self.log_message.emit(f"Total Capture Time (Queue): {capture_time:.2f} seconds")

        if finalize_time > 0:
            self.log_message.emit(f"Pipeline Finalize Time: {finalize_time:.2f} seconds")
            if copy_time > 0:
                self.log_message.emit(f"  - File Copy Time: {copy_time:.2f} seconds")
                self.log_message.emit(f"  - Queue Wait Time: {finalize_time - copy_time:.2f} seconds")

        if stitch_time > 0:
            self.log_message.emit(f"Final Stitching Time: {stitch_time:.2f} seconds")

        self.log_message.emit(f"Total Process Time: {total_time:.2f} seconds")

        # Performance summary
        total_points = len(self.interpolated_grid_points)
        if total_points > 0:
            avg_time_per_point = total_time / total_points
            self.log_message.emit(f"Average Time per Point: {avg_time_per_point:.2f} seconds")

        # Show optimization info
        if self.use_memory_buffer:
            self.log_message.emit(f"✓ In-memory buffer optimization enabled")
            self.log_message.emit(f"  - Staging: System RAM (PNG fast compression)")
            self.log_message.emit(f"  - Final output: {self.final_output_root}")
        else:
            self.log_message.emit("⚠️ Memory buffer optimization disabled (direct save)")

        # Show file locations
        self.log_message.emit(f"Output folder: {self.output_folder}")

        total_points = len(self.interpolated_grid_points)
        self.log_message.emit(f"Total Images Captured: {total_points}")
        self.log_message.emit(f"Final Stitching: Bilinear with {self.overlap_x_percent}% X / {self.overlap_y_percent}% Y overlap")

        self.log_message.emit("-" * 50)

    def _perform_final_stitching(self):
        """
        Perform final stitching using ImageStitcher from Stitch.py

        Returns:
            numpy array: Stitched image or None if failed
        """
        try:
            # Import ImageStitcher from Stitch.py (located in project root)
            import sys
            import os
            # Get the project root directory (parent of Software directory)
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            stitch_path = os.path.join(project_root, 'Stitch.py')

            if not os.path.exists(stitch_path):
                self.log_message.emit(f"❌ Stitch.py not found at: {stitch_path}")
                return None

            try:
                spec = importlib.util.spec_from_file_location("Stitch", stitch_path)
                if spec is None or spec.loader is None:
                    self.log_message.emit(f"❌ Could not load Stitch.py from: {stitch_path}")
                    return None

                stitch_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(stitch_module)

                if not hasattr(stitch_module, 'ImageStitcher'):
                    self.log_message.emit("❌ ImageStitcher class not found in Stitch.py")
                    return None

                ImageStitcher = stitch_module.ImageStitcher
                self.log_message.emit("✅ Successfully imported ImageStitcher from Stitch.py")
            except Exception as e:
                self.log_message.emit(f"❌ Error importing Stitch.py: {e}")
                return None

            # Get all captured images from memory buffer
            captured_images = {}
            for filename, png_data in self._memory_buffer.items():
                # Decode PNG data back to numpy array
                nparr = np.frombuffer(png_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                if img is not None:
                    # Extract position from filename (format: stitch_bilinear_pXXX_X...Y...Z...)
                    # We need to parse the filename to get grid position
                    parts = filename.split('_')
                    if len(parts) >= 6:
                        try:
                            point_str = parts[2]  # pXXX (corrected from parts[3])
                            point_index = int(point_str[1:])  # Remove 'p' prefix

                            # Find corresponding grid position from interpolated_grid_points
                            if point_index < len(self.interpolated_grid_points):
                                x, y, z = self.interpolated_grid_points[point_index]
                                # Use point index as grid position for serpentine stitching
                                captured_images[point_index] = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        except (ValueError, IndexError) as e:
                            self.log_message.emit(f"Warning: Could not parse position from filename {filename}: {e}")

            if not captured_images:
                self.log_message.emit("❌ No images found in memory buffer for stitching")
                return None

            self.log_message.emit(f"📸 Found {len(captured_images)} images for final stitching")

            # Create grid from captured images using serpentine pattern
            # The images are captured in the order of interpolated_grid_points (serpentine)
            total_images = len(captured_images)
            if total_images == 0:
                self.log_message.emit("❌ No images to create grid from")
                return None

            # Determine grid dimensions from the interpolated points
            # We need to find the grid structure from the point coordinates
            all_points = self.interpolated_grid_points
            x_coords = [x for x, y, z in all_points]
            y_coords = [y for x, y, z in all_points]

            unique_x = sorted(list(set(x_coords)))
            unique_y = sorted(list(set(y_coords)))

            grid_rows = len(unique_y)
            grid_cols = len(unique_x)

            self.log_message.emit(f"🔲 Creating grid: {grid_rows}x{grid_cols} from {total_images} images")

            # Create grid array in serpentine order (corrected pattern)
            # Pattern should be: 9 10 11 12
            #                   8  7  6  5
            #                   1  2  3  4
            grid = []
            for r in range(grid_rows):
                row = []
                for c in range(grid_cols):
                    # Calculate point index in serpentine pattern
                    # Start from bottom row (highest Y) and work upwards
                    actual_row = grid_rows - 1 - r  # Flip row order

                    if actual_row % 2 == 0:  # Even row from bottom (left to right)
                        point_index = actual_row * grid_cols + c
                    else:  # Odd row from bottom (right to left)
                        point_index = actual_row * grid_cols + (grid_cols - 1 - c)

                    if point_index in captured_images:
                        row.append(captured_images[point_index])
                    else:
                        row.append(None)  # Missing image
                grid.append(row)

            # Find first valid image to get dimensions
            sample_img = None
            for row in grid:
                for img in row:
                    if img is not None:
                        sample_img = img
                        break
                if sample_img is not None:
                    break

            if sample_img is None:
                self.log_message.emit("❌ No valid images found in grid")
                return None

            # Calculate pixel overlap from percentages
            img_h, img_w = sample_img.shape[:2]
            overlap_x_px = int(img_w * self.overlap_x_percent / 100.0)
            overlap_y_px = int(img_h * self.overlap_y_percent / 100.0)

            self.log_message.emit(f"🔗 Final stitching with overlap X: {self.overlap_x_percent}% ({overlap_x_px}px), Y: {self.overlap_y_percent}% ({overlap_y_px}px)")
            self.log_message.emit(f"📐 Image dimensions: {img_w}x{img_h}, Grid: {grid_rows}x{grid_cols}")

            # Create name_grid from captured metadata for ImageStitcher
            name_grid = []
            for r in range(grid_rows):
                name_row = []
                for c in range(grid_cols):
                    # Calculate point index in serpentine pattern
                    actual_row = grid_rows - 1 - r  # Flip row order

                    if actual_row % 2 == 0:  # Even row from bottom (left to right)
                        point_index = actual_row * grid_cols + c
                    else:  # Odd row from bottom (right to left)
                        point_index = actual_row * grid_cols + (grid_cols - 1 - c)

                    # Find filename from metadata
                    filename = None
                    for metadata_entry in self.capture_metadata:
                        if metadata_entry.get('point_index') == point_index:
                            filename = metadata_entry.get('filename')
                            break

                    name_row.append(filename)
                name_grid.append(name_row)

            self.log_message.emit(f"📝 Created name_grid: {len(name_grid)}x{len(name_grid[0]) if name_grid else 0}")

            # Create stitcher and perform stitching
            stitcher = ImageStitcher(grid, name_grid, "manual", overlap_x_px, overlap_y_px)
            result = stitcher.stitch()

            # Handle tuple return (stitched_image, metadata) from ImageStitcher
            if result is not None and isinstance(result, tuple) and len(result) == 2:
                stitched_image, stitcher_metadata = result
                if stitched_image is not None:
                    self.log_message.emit(f"✅ Final stitching successful! Result size: {stitched_image.shape[1]}x{stitched_image.shape[0]}")
                    return stitched_image
                else:
                    self.log_message.emit("❌ Stitched image is None")
                    return None
            elif result is not None:
                # Handle single return (backward compatibility)
                self.log_message.emit(f"✅ Final stitching successful! Result size: {result.shape[1]}x{result.shape[0]}")
                return result
            else:
                self.log_message.emit("❌ Stitching returned None")
                return None

        except Exception as e:
            self.log_message.emit(f"❌ Error in final stitching: {e}")
            self.log_message.emit(f"❌ Traceback: {traceback.format_exc()}")
            return None

    def _calculate_grid_position(self, point_index):
        """
        Calculate grid position (row, col) for a given point index

        Args:
            point_index: Index of the point in the interpolated_grid_points list

        Returns:
            dict: {"row": int, "col": int}
        """
        try:
            if not hasattr(self, 'interpolated_grid_points') or not self.interpolated_grid_points:
                return {"row": -1, "col": -1}

            # Get all unique coordinates to determine grid dimensions
            all_points = self.interpolated_grid_points
            x_coords = sorted(list(set([x for x, y, z in all_points])))
            y_coords = sorted(list(set([y for x, y, z in all_points])))

            if point_index >= len(all_points):
                return {"row": -1, "col": -1}

            # Get coordinates of current point
            current_x, current_y, current_z = all_points[point_index]

            # Find row and column indices
            col = x_coords.index(current_x) if current_x in x_coords else -1
            row = y_coords.index(current_y) if current_y in y_coords else -1

            return {"row": row, "col": col}

        except Exception as e:
            self.log_message.emit(f"Warning: Could not calculate grid position for point {point_index}: {e}")
            return {"row": -1, "col": -1}

    def _save_metadata_file(self):
        """
        Save collected metadata to JSON file
        """
        try:
            if not self.capture_metadata:
                self.log_message.emit("⚠️ No metadata collected to save")
                return False

            # Create metadata filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.metadata_filename = f"stitching_metadata_{timestamp}.json"
            metadata_path = os.path.join(self.output_folder, self.metadata_filename)

            # Prepare metadata structure
            metadata_structure = {
                "stitching_info": {
                    "timestamp": datetime.now().isoformat(),
                    "overlap_x_percent": self.overlap_x_percent,
                    "overlap_y_percent": self.overlap_y_percent,
                    "total_images": len(self.capture_metadata),
                    "grid_points": len(self.interpolated_grid_points) if hasattr(self, 'interpolated_grid_points') else 0
                },
                "image_metadata": self.capture_metadata
            }

            # Save to JSON file
            import json
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata_structure, f, indent=2, ensure_ascii=False)

            file_size_kb = os.path.getsize(metadata_path) / 1024
            self.log_message.emit(f"📄 Metadata saved: {self.metadata_filename} ({file_size_kb:.1f}KB)")
            self.log_message.emit(f"📁 Location: {metadata_path}")
            return True

        except Exception as e:
            self.log_message.emit(f"❌ Error saving metadata file: {e}")
            return False

    def _save_stitched_result(self, stitched_image):
        """
        Save the final stitched result to disk

        Args:
            stitched_image: numpy array (H, W, 3) RGB
        """
        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"final_stitched_{self.overlap_x_percent}x{self.overlap_y_percent}_{timestamp}.png"
            filepath = os.path.join(self.output_folder, filename)

            # Convert RGB to BGR for OpenCV
            bgr_image = cv2.cvtColor(stitched_image, cv2.COLOR_RGB2BGR)

            # Save image
            success = cv2.imwrite(filepath, bgr_image)
            if success:
                file_size_mb = os.path.getsize(filepath) / (1024 * 1024)
                self.log_message.emit(f"💾 Final stitched image saved: {filename} ({file_size_mb:.2f}MB)")
                self.log_message.emit(f"📁 Location: {filepath}")
            else:
                self.log_message.emit(f"❌ Failed to save stitched image: {filename}")

        except Exception as e:
            self.log_message.emit(f"❌ Error saving stitched result: {e}")
