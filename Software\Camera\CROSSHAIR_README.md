# Fitur Crosshair pada ROILabel

## Deskripsi
Fitur crosshair menampilkan tanda plus (+) berwarna hitam di titik tengah gambar preview. Crosshair ini berguna untuk:
- Menunjukkan titik tengah pixel gambar secara visual
- Referensi untuk kalibrasi dan alignment
- Membantu dalam positioning yang akurat

## Fitur yang Tersedia

### 1. <PERSON><PERSON><PERSON> Default
- **Warna**: <PERSON>am (RGB: 0, 0, 0)
- **Ukuran**: 20 pixel (panjang garis)
- **Ke<PERSON>balan**: 2 pixel
- **Status**: Aktif secara default

### 2. Metode Kontrol Crosshair

#### `set_crosshair_visible(visible: bool)`
Mengatur visibilitas crosshair
```python
roi_label.set_crosshair_visible(True)   # Tampilkan crosshair
roi_label.set_crosshair_visible(False)  # Sembunyikan crosshair
```

#### `toggle_crosshair() -> bool`
Toggle on/off crosshair dan men<PERSON>likan status
```python
is_visible = roi_label.toggle_crosshair()
print(f"Crosshair {'aktif' if is_visible else 'nonaktif'}")
```

#### `set_crosshair_color(color: QColor)`
Mengatur warna crosshair
```python
from PyQt5.QtGui import QColor
roi_label.set_crosshair_color(QColor(255, 0, 0))  # Merah
roi_label.set_crosshair_color(QColor(0, 255, 0))  # Hijau
roi_label.set_crosshair_color(QColor(0, 0, 255))  # Biru
```

#### `set_crosshair_size(size: int)`
Mengatur ukuran crosshair (panjang garis dalam pixel)
```python
roi_label.set_crosshair_size(30)  # Crosshair 30 pixel
roi_label.set_crosshair_size(15)  # Crosshair 15 pixel
```

#### `get_crosshair_pixel_coordinates() -> QPoint`
Mendapatkan koordinat pixel absolut dari titik tengah crosshair
```python
coords = roi_label.get_crosshair_pixel_coordinates()
if coords:
    print(f"Koordinat tengah: ({coords.x()}, {coords.y()})")
```

## Penggunaan dalam Aplikasi Utama

### 1. Menambahkan Kontrol Crosshair ke UI
```python
# Tombol toggle crosshair
crosshair_btn = QPushButton("Toggle Crosshair")
crosshair_btn.clicked.connect(lambda: self.roi_label.toggle_crosshair())

# Tombol untuk mendapatkan koordinat tengah
center_btn = QPushButton("Get Center")
center_btn.clicked.connect(self.get_center_coordinates)

def get_center_coordinates(self):
    coords = self.roi_label.get_crosshair_pixel_coordinates()
    if coords:
        print(f"Center: ({coords.x()}, {coords.y()})")
```

### 2. Kustomisasi Crosshair
```python
# Setup crosshair dengan warna dan ukuran khusus
roi_label.set_crosshair_color(QColor(255, 255, 0))  # Kuning
roi_label.set_crosshair_size(25)  # 25 pixel
roi_label.set_crosshair_visible(True)
```

### 3. Integrasi dengan Move To
```python
def move_to_center(self):
    """Pindah ke titik tengah crosshair"""
    coords = self.roi_label.get_crosshair_pixel_coordinates()
    if coords and self.roi_label.move_controller:
        self.roi_label.move_controller.move_to_pixel(coords.x(), coords.y())
```

## Koordinat Pixel yang Diharapkan

Untuk resolusi 1920x1080:
- **Titik tengah**: (960, 540)
- **Crosshair akan menunjuk ke koordinat ini**

Untuk resolusi lain:
- **Titik tengah**: (width/2, height/2)

## Logging dan Debug

Crosshair akan otomatis log koordinat tengah saat pertama kali menerima gambar:
```
[INFO] Crosshair center pixel coordinates: (960, 540)
[INFO] Expected center for 1920x1080: (960, 540)
```

## Contoh Penggunaan

Lihat file `crosshair_example.py` untuk demo lengkap fitur crosshair.

## Catatan Teknis

1. **Crosshair selalu di tengah**: Posisi crosshair otomatis dihitung berdasarkan ukuran gambar yang ditampilkan
2. **Koordinat akurat**: Metode `get_crosshair_pixel_coordinates()` menggunakan sistem konversi yang sama dengan ROI selection
3. **Update otomatis**: Crosshair akan ter-update otomatis saat gambar berubah ukuran atau posisi
4. **Performance**: Crosshair digambar langsung di paintEvent tanpa overhead tambahan yang signifikan
