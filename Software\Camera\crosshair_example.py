"""
<PERSON><PERSON>h penggunaan fitur Crosshair pada ROILabel
File ini menunjukkan cara menggunakan crosshair untuk menampilkan titik tengah gambar
"""

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor
import sys
import os

# Import ROILabel
sys.path.append(os.path.dirname(__file__))
from RoiLabel import ROILabel

class CrosshairDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Crosshair Demo - ROI Label")
        self.setGeometry(100, 100, 800, 600)
        
        # Widget utama
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # ROI Label dengan crosshair
        self.roi_label = ROILabel()
        self.roi_label.setMinimumSize(640, 480)
        self.roi_label.setStyleSheet("border: 2px solid gray;")
        layout.addWidget(self.roi_label)
        
        # Panel kontrol crosshair
        control_layout = QHBoxLayout()
        
        # Tombol toggle crosshair
        self.toggle_btn = QPushButton("Hide Crosshair")
        self.toggle_btn.clicked.connect(self.toggle_crosshair)
        control_layout.addWidget(self.toggle_btn)
        
        # Tombol ubah warna
        color_btn = QPushButton("Change Color")
        color_btn.clicked.connect(self.change_crosshair_color)
        control_layout.addWidget(color_btn)
        
        # Tombol ubah ukuran
        size_btn = QPushButton("Change Size")
        size_btn.clicked.connect(self.change_crosshair_size)
        control_layout.addWidget(size_btn)
        
        # Tombol get coordinates
        coord_btn = QPushButton("Get Center Coordinates")
        coord_btn.clicked.connect(self.get_center_coordinates)
        control_layout.addWidget(coord_btn)
        
        layout.addLayout(control_layout)
        
        # Label untuk menampilkan informasi
        self.info_label = QLabel("Crosshair aktif - Klik tombol untuk mengontrol")
        self.info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.info_label)
        
        # Variabel untuk demo
        self.color_index = 0
        self.size_index = 0
        self.colors = [QColor(0, 0, 0), QColor(255, 0, 0), QColor(0, 255, 0), QColor(0, 0, 255), QColor(255, 255, 0)]
        self.sizes = [20, 30, 40, 15, 25]
        
        # Timer untuk simulasi gambar (opsional)
        self.setup_demo_image()
    
    def setup_demo_image(self):
        """
        Setup gambar demo untuk menampilkan crosshair
        """
        from PyQt5.QtGui import QPixmap, QPainter, QBrush
        
        # Buat pixmap demo
        pixmap = QPixmap(640, 480)
        pixmap.fill(QColor(100, 150, 200))
        
        # Gambar beberapa elemen untuk referensi
        painter = QPainter(pixmap)
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawEllipse(50, 50, 100, 100)
        painter.drawEllipse(490, 330, 100, 100)
        painter.drawRect(250, 200, 140, 80)
        painter.end()
        
        # Set ke ROI label
        self.roi_label.setPixmap(pixmap)
        self.roi_label.original_pixmap = pixmap
        
        # Set resolusi untuk demo
        self.roi_label.actual_resolution = (640, 480)
    
    def toggle_crosshair(self):
        """
        Toggle crosshair on/off
        """
        is_visible = self.roi_label.toggle_crosshair()
        self.toggle_btn.setText("Hide Crosshair" if is_visible else "Show Crosshair")
        self.info_label.setText(f"Crosshair {'aktif' if is_visible else 'nonaktif'}")
    
    def change_crosshair_color(self):
        """
        Ubah warna crosshair
        """
        self.color_index = (self.color_index + 1) % len(self.colors)
        color = self.colors[self.color_index]
        self.roi_label.set_crosshair_color(color)
        color_name = ["Hitam", "Merah", "Hijau", "Biru", "Kuning"][self.color_index]
        self.info_label.setText(f"Warna crosshair: {color_name}")
    
    def change_crosshair_size(self):
        """
        Ubah ukuran crosshair
        """
        self.size_index = (self.size_index + 1) % len(self.sizes)
        size = self.sizes[self.size_index]
        self.roi_label.set_crosshair_size(size)
        self.info_label.setText(f"Ukuran crosshair: {size} pixel")
    
    def get_center_coordinates(self):
        """
        Dapatkan koordinat pixel dari titik tengah crosshair
        """
        coords = self.roi_label.get_crosshair_pixel_coordinates()
        if coords:
            self.info_label.setText(f"Koordinat tengah: ({coords.x()}, {coords.y()}) pixel")
            print(f"[INFO] Crosshair center coordinates: ({coords.x()}, {coords.y()})")
        else:
            self.info_label.setText("Tidak dapat mendapatkan koordinat - tidak ada gambar")

def main():
    app = QApplication(sys.argv)
    demo = CrosshairDemo()
    demo.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
